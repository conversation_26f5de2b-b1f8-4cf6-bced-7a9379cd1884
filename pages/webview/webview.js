// pages/webview/webview.js
Page({
  data: {
    webUrl: ''
  },

  onLoad(options) {
    // 设置Web版本的URL
    // 在实际部署时，这里应该是你的Web版本的实际URL
    const baseUrl = options.url || 'https://your-domain.com/index.html';
    
    this.setData({
      webUrl: baseUrl
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '文档搜索工具'
    });
  },

  onMessage(e) {
    // 接收来自WebView的消息
    console.log('收到WebView消息:', e.detail.data);
    
    const data = e.detail.data[0];
    if (!data) return;

    switch (data.type) {
      case 'download':
        this.handleDownload(data.payload);
        break;
      case 'share':
        this.handleShare(data.payload);
        break;
      case 'storage':
        this.handleStorage(data.payload);
        break;
      default:
        console.log('未知消息类型:', data.type);
    }
  },

  onError(e) {
    console.error('WebView加载错误:', e);
    wx.showModal({
      title: '加载失败',
      content: '网页加载失败，请检查网络连接后重试',
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
  },

  onLoad() {
    console.log('WebView加载完成');
  },

  // 处理下载请求
  handleDownload(payload) {
    const { url, filename } = payload;
    
    wx.showLoading({
      title: '下载中...'
    });

    wx.downloadFile({
      url: url,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.saveFile({
            tempFilePath: res.tempFilePath,
            success: (saveRes) => {
              wx.hideLoading();
              wx.showToast({
                title: '下载成功',
                icon: 'success'
              });
              
              // 发送下载成功消息回WebView
              this.postMessageToWebView({
                type: 'downloadSuccess',
                payload: {
                  filename: filename,
                  savedFilePath: saveRes.savedFilePath
                }
              });
            },
            fail: (err) => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
              console.error('文件保存失败:', err);
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
        console.error('下载失败:', err);
      }
    });
  },

  // 处理分享请求
  handleShare(payload) {
    const { title, url, description } = payload;
    
    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 分享给朋友
            wx.showToast({
              title: '功能开发中',
              icon: 'none'
            });
            break;
          case 1:
            // 分享到朋友圈
            wx.showToast({
              title: '功能开发中',
              icon: 'none'
            });
            break;
          case 2:
            // 复制链接
            wx.setClipboardData({
              data: url,
              success: () => {
                wx.showToast({
                  title: '链接已复制',
                  icon: 'success'
                });
              }
            });
            break;
        }
      }
    });
  },

  // 处理存储请求
  handleStorage(payload) {
    const { action, key, data } = payload;
    
    switch (action) {
      case 'set':
        try {
          wx.setStorageSync(key, data);
          this.postMessageToWebView({
            type: 'storageSuccess',
            payload: { action: 'set', key }
          });
        } catch (e) {
          console.error('存储数据失败:', e);
        }
        break;
      case 'get':
        try {
          const value = wx.getStorageSync(key);
          this.postMessageToWebView({
            type: 'storageData',
            payload: { key, data: value }
          });
        } catch (e) {
          console.error('读取数据失败:', e);
        }
        break;
      case 'remove':
        try {
          wx.removeStorageSync(key);
          this.postMessageToWebView({
            type: 'storageSuccess',
            payload: { action: 'remove', key }
          });
        } catch (e) {
          console.error('删除数据失败:', e);
        }
        break;
    }
  },

  // 向WebView发送消息
  postMessageToWebView(message) {
    // 注意：小程序向WebView发送消息需要特殊处理
    // 这里只是示例，实际实现可能需要其他方式
    console.log('向WebView发送消息:', message);
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '文档搜索下载工具',
      desc: '高效便捷的文档搜索下载工具',
      path: '/pages/webview/webview'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '文档搜索下载工具 - 高效便捷的文档搜索',
      query: ''
    };
  }
});
