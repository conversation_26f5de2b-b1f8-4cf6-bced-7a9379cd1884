/* pages/index/index.wxss */

page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  padding: 40rpx 30rpx;
  min-height: 100vh;
}

.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.app-logo {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 20rpx;
}

.app-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.version-selector {
  margin-bottom: 60rpx;
}

.version-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
}

.version-options {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.version-option {
  background: white;
  border: 3rpx solid #f0f0f0;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
  transition: all 0.3s ease;
  text-align: left;
}

.version-option:active {
  transform: scale(0.98);
  border-color: #667eea;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.2);
}

.option-icon {
  font-size: 60rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.option-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.feature-tag {
  background: #f0f9ff;
  color: #0369a1;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.option-arrow {
  font-size: 32rpx;
  color: #667eea;
  font-weight: bold;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.quick-action {
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.quick-action:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.2);
}

.quick-icon {
  font-size: 48rpx;
}

.quick-action text {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

.features-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  text-align: center;
  margin-bottom: 50rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40rpx;
}

.feature-item {
  text-align: center;
}

.feature-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: 30rpx;
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}
