// pages/index/index.js
Page({
  data: {
    
  },

  onLoad() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '文档搜索工具'
    });
  },

  // 使用Web版本
  useWebVersion() {
    wx.showLoading({
      title: '加载中...'
    });

    // 这里需要替换为你的实际Web版本URL
    // 可以是本地服务器地址（开发时）或线上地址（生产时）
    const webUrl = 'http://localhost:8080/index.html'; // 开发环境
    // const webUrl = 'https://your-domain.com/index.html'; // 生产环境

    wx.navigateTo({
      url: `/pages/webview/webview?url=${encodeURIComponent(webUrl)}`,
      success: () => {
        wx.hideLoading();
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('跳转失败:', err);
        wx.showModal({
          title: '提示',
          content: 'Web版本暂时无法访问，请使用小程序原生版本',
          showCancel: false
        });
      }
    });
  },

  // 使用小程序原生版本
  useNativeVersion() {
    wx.switchTab({
      url: '/pages/search/search',
      fail: (err) => {
        // 如果switchTab失败，使用navigateTo
        wx.navigateTo({
          url: '/pages/search/search'
        });
      }
    });
  },

  // 快捷搜索
  quickSearch(e) {
    const type = e.currentTarget.dataset.type;
    
    // 构建搜索参数
    const searchPresets = {
      academic: {
        query: '学术 论文',
        engine: 'baidu',
        fileTypes: ['pdf'],
        site: ''
      },
      report: {
        query: '行业报告 白皮书',
        engine: 'baidu',
        fileTypes: ['pdf', 'doc'],
        site: ''
      },
      wechat: {
        query: '',
        engine: 'baidu',
        fileTypes: [],
        site: 'weixin.qq.com'
      },
      tech: {
        query: '技术文档 API',
        engine: 'bing',
        fileTypes: ['pdf', 'doc'],
        site: ''
      }
    };

    const preset = searchPresets[type];
    if (!preset) return;

    // 询问用户使用哪个版本
    wx.showActionSheet({
      itemList: ['Web版本', '小程序原生版本'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // Web版本
          this.quickSearchWeb(preset);
        } else {
          // 小程序原生版本
          this.quickSearchNative(preset);
        }
      }
    });
  },

  // Web版本快捷搜索
  quickSearchWeb(preset) {
    const params = new URLSearchParams(preset).toString();
    const webUrl = `http://localhost:8080/index.html?${params}`;
    
    wx.navigateTo({
      url: `/pages/webview/webview?url=${encodeURIComponent(webUrl)}`
    });
  },

  // 小程序原生版本快捷搜索
  quickSearchNative(preset) {
    // 将搜索参数存储到全局数据中
    const app = getApp();
    app.globalData.quickSearchPreset = preset;
    
    wx.navigateTo({
      url: '/pages/search/search'
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '文档搜索下载工具',
      desc: '高效便捷的文档搜索下载工具，支持多引擎搜索和批量下载',
      path: '/pages/index/index'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '文档搜索下载工具 - 高效便捷的文档搜索',
      query: ''
    };
  }
});
