<!-- pages/index/index.wxml -->
<view class="container">
  <view class="welcome-card">
    <view class="app-logo">📄</view>
    <view class="app-title">文档搜索工具</view>
    <view class="app-desc">高效便捷的文档搜索下载工具</view>
    
    <view class="version-selector">
      <view class="version-title">选择使用版本</view>
      
      <view class="version-options">
        <view class="version-option" bindtap="useWebVersion">
          <view class="option-icon">🌐</view>
          <view class="option-content">
            <view class="option-title">Web版本</view>
            <view class="option-desc">功能完整，界面美观，推荐使用</view>
            <view class="option-features">
              <text class="feature-tag">✓ 完整功能</text>
              <text class="feature-tag">✓ 响应式设计</text>
              <text class="feature-tag">✓ 实时搜索</text>
            </view>
          </view>
          <view class="option-arrow">→</view>
        </view>
        
        <view class="version-option" bindtap="useNativeVersion">
          <view class="option-icon">📱</view>
          <view class="option-content">
            <view class="option-title">小程序原生版本</view>
            <view class="option-desc">小程序原生界面，轻量快速</view>
            <view class="option-features">
              <text class="feature-tag">✓ 原生体验</text>
              <text class="feature-tag">✓ 快速启动</text>
              <text class="feature-tag">✓ 离线缓存</text>
            </view>
          </view>
          <view class="option-arrow">→</view>
        </view>
      </view>
    </view>
    
    <view class="quick-actions">
      <view class="quick-action" bindtap="quickSearch" data-type="academic">
        <view class="quick-icon">📄</view>
        <text>学术论文</text>
      </view>
      <view class="quick-action" bindtap="quickSearch" data-type="report">
        <view class="quick-icon">📊</view>
        <text>行业报告</text>
      </view>
      <view class="quick-action" bindtap="quickSearch" data-type="wechat">
        <view class="quick-icon">📱</view>
        <text>公众号文章</text>
      </view>
      <view class="quick-action" bindtap="quickSearch" data-type="tech">
        <view class="quick-icon">📚</view>
        <text>技术文档</text>
      </view>
    </view>
  </view>
  
  <view class="features-section">
    <view class="section-title">功能特色</view>
    <view class="features-grid">
      <view class="feature-item">
        <view class="feature-icon">🔍</view>
        <view class="feature-title">多引擎搜索</view>
        <view class="feature-desc">支持百度、Bing、Google等多个搜索引擎</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">📥</view>
        <view class="feature-title">批量下载</view>
        <view class="feature-desc">支持选择多个文件同时下载管理</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">🎯</view>
        <view class="feature-title">精准筛选</view>
        <view class="feature-desc">按文件类型、站点、时间精准筛选</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">⚡</view>
        <view class="feature-title">高效便捷</view>
        <view class="feature-desc">简洁界面，操作便捷，快速找到所需文档</view>
      </view>
    </view>
  </view>
</view>
