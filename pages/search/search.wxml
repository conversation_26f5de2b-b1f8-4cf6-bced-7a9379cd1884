<!-- pages/search/search.wxml -->
<view class="container">
  <!-- 搜索卡片 -->
  <view class="card">
    <view class="card-header">
      <text>文档搜索</text>
    </view>
    <view class="card-body">
      <!-- 搜索框 -->
      <view class="input-group">
        <label class="input-label">搜索关键词</label>
        <input 
          class="input-field" 
          placeholder="请输入搜索关键词" 
          value="{{searchQuery}}"
          bindinput="onSearchInput"
          confirm-type="search"
          bindconfirm="onSearch"
        />
      </view>

      <!-- 搜索引擎选择 -->
      <view class="input-group">
        <label class="input-label">搜索引擎</label>
        <view class="engine-selector">
          <view 
            class="tag {{selectedEngine === 'baidu' ? 'active' : ''}}"
            bindtap="selectEngine"
            data-engine="baidu"
          >
            百度
          </view>
          <view 
            class="tag {{selectedEngine === 'bing' ? 'active' : ''}}"
            bindtap="selectEngine"
            data-engine="bing"
          >
            Bing
          </view>
          <view 
            class="tag {{selectedEngine === 'google' ? 'active' : ''}}"
            bindtap="selectEngine"
            data-engine="google"
          >
            Google
          </view>
        </view>
      </view>

      <!-- 文件类型选择 -->
      <view class="input-group">
        <label class="input-label">文件类型</label>
        <view class="filetype-selector">
          <view 
            wx:for="{{fileTypes}}" 
            wx:key="type"
            class="tag {{item.selected ? 'active' : ''}}"
            bindtap="toggleFileType"
            data-type="{{item.type}}"
          >
            {{item.label}}
          </view>
        </view>
      </view>

      <!-- 高级选项 -->
      <view class="advanced-options" wx:if="{{showAdvanced}}">
        <view class="input-group">
          <label class="input-label">限定站点 (可选)</label>
          <input 
            class="input-field" 
            placeholder="如: weixin.qq.com" 
            value="{{siteFilter}}"
            bindinput="onSiteInput"
          />
        </view>

        <view class="input-group">
          <label class="input-label">时间范围</label>
          <picker
            mode="selector"
            range="{{timeRanges}}"
            range-key="label"
            value="{{selectedTimeRange}}"
            bindchange="onTimeRangeChange"
          >
            <view class="picker-display input-field">
              {{timeRanges[selectedTimeRange].label}}
            </view>
          </picker>
        </view>
      </view>

      <!-- 高级选项切换 -->
      <view class="advanced-toggle" bindtap="toggleAdvanced">
        <text>{{showAdvanced ? '收起' : '展开'}}高级选项</text>
        <text class="arrow {{showAdvanced ? 'up' : 'down'}}">▼</text>
      </view>

      <!-- 搜索按钮 -->
      <button 
        class="btn btn-primary btn-large search-btn" 
        bindtap="onSearch"
        loading="{{searching}}"
        disabled="{{!searchQuery || searching}}"
      >
        {{searching ? '搜索中...' : '开始搜索'}}
      </button>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="card" wx:if="{{searchHistory.length > 0}}">
    <view class="card-header">
      <text>搜索历史</text>
      <text class="clear-history" bindtap="clearHistory">清空</text>
    </view>
    <view class="history-list">
      <view 
        wx:for="{{searchHistory}}" 
        wx:key="id"
        class="history-item"
        bindtap="useHistoryItem"
        data-item="{{item}}"
      >
        <view class="history-content">
          <view class="history-query">{{item.query}}</view>
          <view class="history-meta">
            {{item.engine}} · {{item.fileTypes.join(', ')}}
            <text wx:if="{{item.site}}"> · {{item.site}}</text>
          </view>
        </view>
        <view class="history-time">
          {{item.timeAgo}}
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷搜索 -->
  <view class="card">
    <view class="card-header">
      <text>快捷搜索</text>
    </view>
    <view class="quick-search">
      <view 
        wx:for="{{quickSearches}}" 
        wx:key="id"
        class="quick-item"
        bindtap="useQuickSearch"
        data-item="{{item}}"
      >
        <view class="quick-icon">{{item.icon}}</view>
        <view class="quick-content">
          <view class="quick-title">{{item.title}}</view>
          <view class="quick-desc">{{item.description}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
