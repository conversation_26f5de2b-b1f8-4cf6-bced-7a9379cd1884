/* pages/search/search.wxss */

.engine-selector, .filetype-selector, .site-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.input-hint {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  line-height: 1.4;
}

.advanced-options {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.advanced-toggle {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  color: #667eea;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.arrow {
  margin-left: 8rpx;
  transition: transform 0.3s ease;
  font-size: 20rpx;
}

.arrow.up {
  transform: rotate(180deg);
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
}

.picker-display::after {
  content: '▼';
  font-size: 20rpx;
  color: #999;
}

.search-btn {
  width: 100%;
  margin-top: 20rpx;
}

/* 搜索历史样式 */
.clear-history {
  color: #667eea;
  font-size: 26rpx;
  font-weight: normal;
}

.history-list {
  padding: 0;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background-color: #f8f9fa;
}

.history-content {
  flex: 1;
}

.history-query {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.history-meta {
  font-size: 24rpx;
  color: #666;
}

.history-time {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
}

/* 快捷搜索样式 */
.quick-search {
  padding: 0;
}

.quick-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.quick-item:last-child {
  border-bottom: none;
}

.quick-item:active {
  background-color: #f8f9fa;
}

.quick-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 24rpx;
}

.quick-content {
  flex: 1;
}

.quick-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.quick-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
