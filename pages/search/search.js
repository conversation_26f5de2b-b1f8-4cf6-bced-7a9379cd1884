// pages/search/search.js
const app = getApp();

Page({
  data: {
    searchQuery: '',
    selectedEngine: 'baidu',
    siteFilter: '',
    showAdvanced: false,
    searching: false,
    
    fileTypes: [
      { type: 'pdf', label: 'PDF', selected: true },
      { type: 'doc', label: 'Word', selected: true },
      { type: 'docx', label: 'Word', selected: false },
      { type: 'ppt', label: 'PPT', selected: false },
      { type: 'pptx', label: 'PPT', selected: false },
      { type: 'xls', label: 'Excel', selected: false },
      { type: 'xlsx', label: 'Excel', selected: false },
      { type: 'txt', label: 'TXT', selected: false }
    ],
    
    timeRanges: [
      { value: '', label: '不限时间' },
      { value: 'day', label: '最近一天' },
      { value: 'week', label: '最近一周' },
      { value: 'month', label: '最近一月' },
      { value: 'year', label: '最近一年' }
    ],
    selectedTimeRange: 0,
    
    searchHistory: [],
    
    quickSearches: [
      {
        id: 1,
        icon: '📄',
        title: '学术论文',
        description: '搜索PDF格式的学术论文和研究报告',
        query: '学术 论文',
        engine: 'baidu',
        fileTypes: ['pdf'],
        site: ''
      },
      {
        id: 2,
        icon: '📊',
        title: '行业报告',
        description: '搜索各行业的分析报告和白皮书',
        query: '行业报告 白皮书',
        engine: 'baidu',
        fileTypes: ['pdf', 'doc', 'docx'],
        site: ''
      },
      {
        id: 3,
        icon: '📱',
        title: '微信公众号',
        description: '搜索微信公众号文章',
        query: '',
        engine: 'baidu',
        fileTypes: [],
        site: 'weixin.qq.com'
      },
      {
        id: 4,
        icon: '📚',
        title: '技术文档',
        description: '搜索技术文档和API说明',
        query: '技术文档 API',
        engine: 'bing',
        fileTypes: ['pdf', 'doc', 'docx'],
        site: ''
      }
    ]
  },

  onLoad() {
    this.loadSearchHistory();
    this.loadUserSettings();
  },

  onShow() {
    this.loadSearchHistory();
  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = app.globalData.searchHistory.map(item => ({
      ...item,
      timeAgo: this.getTimeAgo(item.timestamp)
    }));
    this.setData({ searchHistory: history.slice(0, 10) });
  },

  // 加载用户设置
  loadUserSettings() {
    const settings = app.globalData.userSettings;
    this.setData({
      selectedEngine: settings.defaultEngine,
      fileTypes: this.data.fileTypes.map(ft => ({
        ...ft,
        selected: settings.defaultFileTypes.includes(ft.type)
      }))
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchQuery: e.detail.value });
  },

  // 站点输入
  onSiteInput(e) {
    this.setData({ siteFilter: e.detail.value });
  },

  // 选择搜索引擎
  selectEngine(e) {
    this.setData({ selectedEngine: e.currentTarget.dataset.engine });
  },

  // 切换文件类型
  toggleFileType(e) {
    const type = e.currentTarget.dataset.type;
    const fileTypes = this.data.fileTypes.map(ft => {
      if (ft.type === type) {
        return { ...ft, selected: !ft.selected };
      }
      return ft;
    });
    this.setData({ fileTypes });
  },

  // 时间范围选择
  onTimeRangeChange(e) {
    this.setData({ selectedTimeRange: e.detail.value });
  },

  // 切换高级选项
  toggleAdvanced() {
    this.setData({ showAdvanced: !this.data.showAdvanced });
  },

  // 执行搜索
  onSearch() {
    if (!this.data.searchQuery.trim()) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    this.setData({ searching: true });

    const selectedFileTypes = this.data.fileTypes
      .filter(ft => ft.selected)
      .map(ft => ft.type);

    const searchParams = {
      query: this.data.searchQuery.trim(),
      engine: this.data.selectedEngine,
      fileTypes: selectedFileTypes,
      site: this.data.siteFilter.trim(),
      timeRange: this.data.timeRanges[this.data.selectedTimeRange].value
    };

    // 添加到搜索历史
    app.addSearchHistory(
      searchParams.query,
      searchParams.engine,
      searchParams.fileTypes,
      searchParams.site
    );

    // 跳转到结果页面
    wx.navigateTo({
      url: `/pages/results/results?params=${encodeURIComponent(JSON.stringify(searchParams))}`,
      success: () => {
        this.setData({ searching: false });
      },
      fail: () => {
        this.setData({ searching: false });
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 使用历史记录
  useHistoryItem(e) {
    const item = e.currentTarget.dataset.item;
    this.setData({
      searchQuery: item.query,
      selectedEngine: item.engine,
      siteFilter: item.site || '',
      fileTypes: this.data.fileTypes.map(ft => ({
        ...ft,
        selected: item.fileTypes.includes(ft.type)
      }))
    });
  },

  // 使用快捷搜索
  useQuickSearch(e) {
    const item = e.currentTarget.dataset.item;
    this.setData({
      searchQuery: item.query,
      selectedEngine: item.engine,
      siteFilter: item.site || '',
      fileTypes: this.data.fileTypes.map(ft => ({
        ...ft,
        selected: item.fileTypes.includes(ft.type)
      }))
    });
    
    if (item.query) {
      this.onSearch();
    }
  },

  // 清空搜索历史
  clearHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.searchHistory = [];
          this.setData({ searchHistory: [] });
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 计算时间差
  getTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 30) return `${days}天前`;
    return time.toLocaleDateString();
  }
});
