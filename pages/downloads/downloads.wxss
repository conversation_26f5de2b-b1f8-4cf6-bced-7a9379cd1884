/* pages/downloads/downloads.wxss */

/* 统计卡片 */
.stats-card {
  margin-bottom: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx 0;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.action-left, .action-right {
  display: flex;
  gap: 16rpx;
}

/* 下载列表 */
.download-list {
  margin-bottom: 40rpx;
}

.download-item {
  margin-bottom: 20rpx;
}

.download-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.file-info {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  flex: 1;
  min-width: 0;
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.file-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.file-type {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.download-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 8rpx;
}

/* 进度条 */
.download-progress {
  margin-bottom: 16rpx;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.download-speed {
  color: #667eea;
  font-weight: 500;
}

/* 完成信息 */
.download-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f0f9ff;
  border-radius: 12rpx;
  border: 1rpx solid #e0f2fe;
}

.result-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.result-text {
  font-size: 28rpx;
  color: #0277bd;
  font-weight: 500;
}

.result-time {
  font-size: 24rpx;
  color: #666;
}

.result-actions {
  display: flex;
  gap: 12rpx;
}

/* 错误信息 */
.download-error {
  padding: 20rpx;
  background: #fef2f2;
  border-radius: 12rpx;
  border: 1rpx solid #fecaca;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.error-icon {
  font-size: 32rpx;
}

.error-text {
  font-size: 26rpx;
  color: #dc2626;
  flex: 1;
}
