// pages/downloads/downloads.js
const app = getApp();

Page({
  data: {
    downloadQueue: [],
    stats: {
      total: 0,
      downloading: 0,
      completed: 0,
      failed: 0
    },
    allPaused: false
  },

  onLoad() {
    this.loadDownloadQueue();
  },

  onShow() {
    this.loadDownloadQueue();
    // 启动定时刷新
    this.startRefreshTimer();
  },

  onHide() {
    // 停止定时刷新
    this.stopRefreshTimer();
  },

  onUnload() {
    this.stopRefreshTimer();
  },

  // 加载下载队列
  loadDownloadQueue() {
    const queue = app.globalData.downloadQueue.map(item => ({
      ...item,
      completedTime: item.status === 'completed' ? this.formatTime(item.timestamp) : null,
      speed: item.status === 'downloading' ? this.getRandomSpeed() : null
    }));

    const stats = this.calculateStats(queue);
    
    this.setData({
      downloadQueue: queue,
      stats
    });
  },

  // 计算统计数据
  calculateStats(queue) {
    return {
      total: queue.length,
      downloading: queue.filter(item => item.status === 'downloading').length,
      completed: queue.filter(item => item.status === 'completed').length,
      failed: queue.filter(item => item.status === 'failed').length
    };
  },

  // 启动刷新定时器
  startRefreshTimer() {
    this.refreshTimer = setInterval(() => {
      this.loadDownloadQueue();
    }, 1000);
  },

  // 停止刷新定时器
  stopRefreshTimer() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  },

  // 获取文件图标
  getFileIcon(fileType) {
    const icons = {
      'PDF': '📄',
      'DOC': '📝',
      'DOCX': '📝',
      'PPT': '📊',
      'PPTX': '📊',
      'XLS': '📈',
      'XLSX': '📈',
      'TXT': '📃'
    };
    return icons[fileType] || '📄';
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '等待中',
      'downloading': '下载中',
      'paused': '已暂停',
      'completed': '已完成',
      'failed': '失败'
    };
    return statusMap[status] || '未知';
  },

  // 获取随机下载速度（模拟）
  getRandomSpeed() {
    const speeds = ['1.2MB/s', '856KB/s', '2.1MB/s', '1.8MB/s', '654KB/s'];
    return speeds[Math.floor(Math.random() * speeds.length)];
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    return date.toLocaleDateString();
  },

  // 暂停下载
  pauseDownload(e) {
    const id = e.currentTarget.dataset.id;
    app.updateDownloadStatus(id, 'paused');
    this.loadDownloadQueue();
    
    wx.showToast({
      title: '已暂停',
      icon: 'success'
    });
  },

  // 继续下载
  resumeDownload(e) {
    const id = e.currentTarget.dataset.id;
    app.updateDownloadStatus(id, 'downloading');
    this.loadDownloadQueue();
    
    wx.showToast({
      title: '继续下载',
      icon: 'success'
    });
  },

  // 重试下载
  retryDownload(e) {
    const id = e.currentTarget.dataset.id;
    app.updateDownloadStatus(id, 'downloading', 0);
    this.loadDownloadQueue();
    
    wx.showToast({
      title: '重新下载',
      icon: 'success'
    });
  },

  // 删除下载
  removeDownload(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个下载任务吗？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.downloadQueue = app.globalData.downloadQueue.filter(
            item => item.id !== id
          );
          this.loadDownloadQueue();
          
          wx.showToast({
            title: '已删除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 全部暂停/继续
  pauseAll() {
    const allPaused = !this.data.allPaused;
    const newStatus = allPaused ? 'paused' : 'downloading';
    
    app.globalData.downloadQueue.forEach(item => {
      if (item.status === 'downloading' || item.status === 'paused') {
        app.updateDownloadStatus(item.id, newStatus);
      }
    });
    
    this.setData({ allPaused });
    this.loadDownloadQueue();
    
    wx.showToast({
      title: allPaused ? '全部暂停' : '全部继续',
      icon: 'success'
    });
  },

  // 清除已完成
  clearCompleted() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有已完成的下载吗？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.downloadQueue = app.globalData.downloadQueue.filter(
            item => item.status !== 'completed'
          );
          this.loadDownloadQueue();
          
          wx.showToast({
            title: '已清除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 清空列表
  clearAll() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有下载记录吗？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.downloadQueue = [];
          this.loadDownloadQueue();
          
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 打开文件
  openFile(e) {
    const id = e.currentTarget.dataset.id;
    const item = app.globalData.downloadQueue.find(item => item.id === id);
    
    if (item) {
      wx.openDocument({
        filePath: item.localPath || '',
        success: () => {
          console.log('文件打开成功');
        },
        fail: (err) => {
          console.error('文件打开失败:', err);
          wx.showToast({
            title: '文件打开失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 分享文件
  shareFile(e) {
    const id = e.currentTarget.dataset.id;
    const item = app.globalData.downloadQueue.find(item => item.id === id);
    
    if (item) {
      wx.showActionSheet({
        itemList: ['发送给朋友', '分享到朋友圈', '复制链接'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              // 发送给朋友
              wx.showToast({
                title: '功能开发中',
                icon: 'none'
              });
              break;
            case 1:
              // 分享到朋友圈
              wx.showToast({
                title: '功能开发中',
                icon: 'none'
              });
              break;
            case 2:
              // 复制链接
              wx.setClipboardData({
                data: item.url,
                success: () => {
                  wx.showToast({
                    title: '链接已复制',
                    icon: 'success'
                  });
                }
              });
              break;
          }
        }
      });
    }
  },

  // 跳转到搜索页
  goToSearch() {
    wx.switchTab({
      url: '/pages/search/search'
    });
  }
});
