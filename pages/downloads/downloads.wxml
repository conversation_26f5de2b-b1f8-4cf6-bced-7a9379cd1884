<!-- pages/downloads/downloads.wxml -->
<view class="container">
  <!-- 下载统计 -->
  <view class="stats-card card">
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-number">{{stats.total}}</view>
        <view class="stat-label">总下载</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.downloading}}</view>
        <view class="stat-label">下载中</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.completed}}</view>
        <view class="stat-label">已完成</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.failed}}</view>
        <view class="stat-label">失败</view>
      </view>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar card" wx:if="{{downloadQueue.length > 0}}">
    <view class="action-left">
      <button 
        class="btn btn-small btn-secondary"
        bindtap="pauseAll"
        disabled="{{stats.downloading === 0}}"
      >
        {{allPaused ? '全部继续' : '全部暂停'}}
      </button>
      <button 
        class="btn btn-small btn-secondary"
        bindtap="clearCompleted"
        disabled="{{stats.completed === 0}}"
      >
        清除已完成
      </button>
    </view>
    <view class="action-right">
      <button 
        class="btn btn-small btn-secondary"
        bindtap="clearAll"
      >
        清空列表
      </button>
    </view>
  </view>

  <!-- 下载队列 -->
  <view class="download-list" wx:if="{{downloadQueue.length > 0}}">
    <view 
      wx:for="{{downloadQueue}}" 
      wx:key="id"
      class="download-item card"
    >
      <view class="download-header">
        <view class="file-info">
          <view class="file-icon">
            <text>{{getFileIcon(item.fileType)}}</text>
          </view>
          <view class="file-details">
            <view class="file-title">{{item.title}}</view>
            <view class="file-meta">
              <text class="file-type">{{item.fileType}}</text>
              <text class="file-size" wx:if="{{item.fileSize}}">{{item.fileSize}}</text>
              <text class="file-source" wx:if="{{item.source}}">{{item.source}}</text>
            </view>
          </view>
        </view>
        <view class="download-actions">
          <view class="status-badge status-{{item.status}}">
            {{getStatusText(item.status)}}
          </view>
          <view class="action-buttons">
            <!-- 暂停/继续按钮 -->
            <button 
              wx:if="{{item.status === 'downloading'}}"
              class="btn btn-small btn-secondary"
              bindtap="pauseDownload"
              data-id="{{item.id}}"
            >
              暂停
            </button>
            <button 
              wx:if="{{item.status === 'paused'}}"
              class="btn btn-small btn-success"
              bindtap="resumeDownload"
              data-id="{{item.id}}"
            >
              继续
            </button>
            <!-- 重试按钮 -->
            <button 
              wx:if="{{item.status === 'failed'}}"
              class="btn btn-small btn-success"
              bindtap="retryDownload"
              data-id="{{item.id}}"
            >
              重试
            </button>
            <!-- 删除按钮 -->
            <button 
              class="btn btn-small btn-secondary"
              bindtap="removeDownload"
              data-id="{{item.id}}"
            >
              删除
            </button>
          </view>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="download-progress" wx:if="{{item.status === 'downloading' || item.status === 'paused'}}">
        <view class="progress">
          <view 
            class="progress-bar" 
            style="width: {{item.progress}}%"
          ></view>
        </view>
        <view class="progress-text">
          <text>{{item.progress}}%</text>
          <text class="download-speed" wx:if="{{item.speed}}">{{item.speed}}</text>
        </view>
      </view>

      <!-- 完成信息 -->
      <view class="download-result" wx:if="{{item.status === 'completed'}}">
        <view class="result-info">
          <text class="result-text">下载完成</text>
          <text class="result-time">{{item.completedTime}}</text>
        </view>
        <view class="result-actions">
          <button 
            class="btn btn-small btn-primary"
            bindtap="openFile"
            data-id="{{item.id}}"
          >
            打开文件
          </button>
          <button 
            class="btn btn-small btn-secondary"
            bindtap="shareFile"
            data-id="{{item.id}}"
          >
            分享
          </button>
        </view>
      </view>

      <!-- 失败信息 -->
      <view class="download-error" wx:if="{{item.status === 'failed'}}">
        <view class="error-message">
          <text class="error-icon">⚠️</text>
          <text class="error-text">{{item.errorMessage || '下载失败'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{downloadQueue.length === 0}}">
    <view class="empty-icon">📥</view>
    <view class="empty-text">
      暂无下载任务<br/>
      去搜索页面添加下载吧
    </view>
    <button 
      class="btn btn-primary"
      bindtap="goToSearch"
    >
      开始搜索
    </button>
  </view>
</view>
