/* pages/results/results.wxss */

.search-info {
  margin-bottom: 20rpx;
}

.search-query {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.query-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.result-count {
  font-size: 26rpx;
  color: #666;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-item {
  padding: 6rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #666;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.selected-count {
  font-size: 26rpx;
  color: #666;
}

/* 结果列表 */
.results-list {
  margin-bottom: 40rpx;
}

.result-item {
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  position: relative;
}

.result-item.selected {
  border: 2rpx solid #667eea;
  background: #f8f9ff;
}

.result-header {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.select-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 8rpx;
}

.select-checkbox.checked {
  background: #667eea;
  border-color: #667eea;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.result-url {
  font-size: 24rpx;
  color: #667eea;
  word-break: break-all;
  line-height: 1.3;
}

.result-actions {
  flex-shrink: 0;
}

.result-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 16rpx;
  padding-left: 60rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.meta-label {
  color: #999;
  margin-right: 8rpx;
}

.meta-value {
  color: #666;
}

.file-type {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.result-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  padding-left: 60rpx;
  word-break: break-all;
}

.result-preview {
  padding-left: 60rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

/* 预览模态框 */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.preview-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 10%;
  left: 5%;
  right: 5%;
  bottom: 10%;
  background: white;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  flex: 1;
  overflow: hidden;
}

.preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 28rpx;
}
