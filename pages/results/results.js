// pages/results/results.js
const app = getApp();

Page({
  data: {
    searchParams: {},
    results: [],
    loading: true,
    searched: false,
    hasMore: false,
    loadingMore: false,
    currentPage: 1,
    selectedCount: 0,
    allSelected: false,
    showPreview: false,
    previewFile: null
  },

  onLoad(options) {
    if (options.params) {
      try {
        const searchParams = JSON.parse(decodeURIComponent(options.params));
        this.setData({ searchParams });
        this.performSearch();
      } catch (e) {
        console.error('解析搜索参数失败:', e);
        this.showError('搜索参数错误');
      }
    } else {
      this.showError('缺少搜索参数');
    }
  },

  // 执行搜索
  async performSearch(page = 1) {
    this.setData({ 
      loading: page === 1,
      loadingMore: page > 1,
      searched: true 
    });

    try {
      const results = await this.searchFiles(this.data.searchParams, page);
      
      if (page === 1) {
        this.setData({
          results: results,
          currentPage: 1,
          hasMore: results.length >= 10 // 假设每页10条
        });
      } else {
        this.setData({
          results: [...this.data.results, ...results],
          currentPage: page,
          hasMore: results.length >= 10
        });
      }
    } catch (error) {
      console.error('搜索失败:', error);
      this.showError('搜索失败，请重试');
    } finally {
      this.setData({ 
        loading: false,
        loadingMore: false 
      });
    }
  },

  // 搜索文件（模拟API调用）
  async searchFiles(params, page = 1) {
    // 这里模拟搜索API调用
    // 在实际应用中，这里会调用真实的搜索引擎API
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockResults = this.generateMockResults(params, page);
        resolve(mockResults);
      }, 1000 + Math.random() * 1000);
    });
  },

  // 生成模拟搜索结果
  generateMockResults(params, page) {
    const results = [];
    const baseIndex = (page - 1) * 10;
    
    for (let i = 0; i < 10; i++) {
      const index = baseIndex + i;
      const fileTypes = params.fileTypes.length > 0 ? params.fileTypes : ['pdf', 'doc', 'docx'];
      const fileType = fileTypes[Math.floor(Math.random() * fileTypes.length)];
      
      results.push({
        id: `result_${index}`,
        title: `${params.query} - 相关文档 ${index + 1}`,
        url: `https://example.com/file${index + 1}.${fileType}`,
        downloadUrl: `https://example.com/download/file${index + 1}.${fileType}`,
        fileType: fileType.toUpperCase(),
        fileSize: this.getRandomFileSize(),
        source: this.getRandomSource(params.site),
        publishTime: this.getRandomDate(),
        description: `这是关于"${params.query}"的相关文档，包含详细的信息和分析...`,
        previewUrl: Math.random() > 0.5 ? `https://example.com/preview/file${index + 1}` : null,
        selected: false
      });
    }
    
    return results;
  },

  // 获取随机文件大小
  getRandomFileSize() {
    const sizes = ['1.2MB', '856KB', '3.4MB', '2.1MB', '654KB', '4.7MB', '1.8MB'];
    return sizes[Math.floor(Math.random() * sizes.length)];
  },

  // 获取随机来源
  getRandomSource(site) {
    if (site) return site;
    const sources = ['百度文库', '豆丁网', '学术网', '官方网站', '研究机构', '教育网站'];
    return sources[Math.floor(Math.random() * sources.length)];
  },

  // 获取随机日期
  getRandomDate() {
    const now = new Date();
    const pastDays = Math.floor(Math.random() * 365);
    const date = new Date(now.getTime() - pastDays * 24 * 60 * 60 * 1000);
    return date.toLocaleDateString();
  },

  // 切换选择状态
  toggleSelect(e) {
    const index = e.currentTarget.dataset.index;
    const results = this.data.results.map((item, i) => {
      if (i === index) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    const selectedCount = results.filter(item => item.selected).length;
    const allSelected = selectedCount === results.length && results.length > 0;
    
    this.setData({ 
      results,
      selectedCount,
      allSelected
    });
  },

  // 全选/取消全选
  selectAll() {
    const allSelected = !this.data.allSelected;
    const results = this.data.results.map(item => ({
      ...item,
      selected: allSelected
    }));
    
    this.setData({
      results,
      allSelected,
      selectedCount: allSelected ? results.length : 0
    });
  },

  // 单个下载
  downloadSingle(e) {
    const index = e.currentTarget.dataset.index;
    const file = this.data.results[index];
    this.downloadFile(file);
  },

  // 批量下载
  batchDownload() {
    const selectedFiles = this.data.results.filter(item => item.selected);
    if (selectedFiles.length === 0) {
      wx.showToast({
        title: '请先选择文件',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '批量下载',
      content: `确定要下载 ${selectedFiles.length} 个文件吗？`,
      success: (res) => {
        if (res.confirm) {
          selectedFiles.forEach(file => {
            this.downloadFile(file);
          });
        }
      }
    });
  },

  // 下载文件
  downloadFile(file) {
    // 添加到下载队列
    const downloadId = app.addToDownloadQueue(file);
    
    // 显示下载提示
    wx.showToast({
      title: '已添加到下载队列',
      icon: 'success'
    });

    // 模拟下载过程
    this.simulateDownload(downloadId, file);
  },

  // 模拟下载过程
  simulateDownload(downloadId, file) {
    app.updateDownloadStatus(downloadId, 'downloading', 0);
    
    const interval = setInterval(() => {
      const currentItem = app.globalData.downloadQueue.find(item => item.id === downloadId);
      if (!currentItem) {
        clearInterval(interval);
        return;
      }
      
      const newProgress = Math.min(currentItem.progress + Math.random() * 20, 100);
      app.updateDownloadStatus(downloadId, 'downloading', newProgress);
      
      if (newProgress >= 100) {
        clearInterval(interval);
        app.updateDownloadStatus(downloadId, 'completed', 100);
        
        // 实际下载文件
        wx.downloadFile({
          url: file.downloadUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              wx.saveFile({
                tempFilePath: res.tempFilePath,
                success: (saveRes) => {
                  console.log('文件保存成功:', saveRes.savedFilePath);
                },
                fail: (err) => {
                  console.error('文件保存失败:', err);
                  app.updateDownloadStatus(downloadId, 'failed', 0);
                }
              });
            }
          },
          fail: (err) => {
            console.error('下载失败:', err);
            app.updateDownloadStatus(downloadId, 'failed', 0);
          }
        });
      }
    }, 500);
  },

  // 预览文件
  previewFile(e) {
    const index = e.currentTarget.dataset.index;
    const file = this.data.results[index];
    
    if (file.previewUrl) {
      this.setData({
        previewFile: file,
        showPreview: true
      });
    } else {
      wx.showToast({
        title: '该文件不支持预览',
        icon: 'none'
      });
    }
  },

  // 关闭预览
  closePreview() {
    this.setData({
      showPreview: false,
      previewFile: null
    });
  },

  // 加载更多
  loadMore() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.performSearch(this.data.currentPage + 1);
    }
  },

  // 返回搜索页
  goBack() {
    wx.navigateBack();
  },

  // 显示错误
  showError(message) {
    wx.showModal({
      title: '错误',
      content: message,
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
  }
});
