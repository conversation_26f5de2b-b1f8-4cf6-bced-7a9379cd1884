<!-- pages/results/results.wxml -->
<view class="container">
  <!-- 搜索信息 -->
  <view class="search-info card">
    <view class="search-query">
      <text class="query-text">{{searchParams.query}}</text>
      <text class="result-count" wx:if="{{results.length > 0}}">
        找到 {{results.length}} 个结果
      </text>
    </view>
    <view class="search-filters">
      <text class="filter-item">{{searchParams.engine}}</text>
      <text class="filter-item" wx:if="{{searchParams.fileTypes.length > 0}}">
        {{searchParams.fileTypes.join(', ')}}
      </text>
      <text class="filter-item" wx:if="{{searchParams.site}}">
        {{searchParams.site}}
      </text>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar card" wx:if="{{results.length > 0}}">
    <view class="action-left">
      <button 
        class="btn btn-small btn-secondary"
        bindtap="selectAll"
      >
        {{allSelected ? '取消全选' : '全选'}}
      </button>
      <text class="selected-count" wx:if="{{selectedCount > 0}}">
        已选择 {{selectedCount}} 个
      </text>
    </view>
    <view class="action-right">
      <button 
        class="btn btn-small btn-success"
        bindtap="batchDownload"
        disabled="{{selectedCount === 0}}"
      >
        批量下载
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>搜索中...</text>
  </view>

  <!-- 搜索结果 -->
  <view class="results-list" wx:if="{{!loading && results.length > 0}}">
    <view 
      wx:for="{{results}}" 
      wx:key="id"
      class="result-item card {{item.selected ? 'selected' : ''}}"
      bindtap="toggleSelect"
      data-index="{{index}}"
    >
      <view class="result-header">
        <view class="select-checkbox {{item.selected ? 'checked' : ''}}">
          <text wx:if="{{item.selected}}">✓</text>
        </view>
        <view class="result-content">
          <view class="result-title">{{item.title}}</view>
          <view class="result-url">{{item.url}}</view>
        </view>
        <view class="result-actions">
          <button 
            class="btn btn-small btn-primary"
            bindtap="downloadSingle"
            data-index="{{index}}"
            catchtap="true"
          >
            下载
          </button>
        </view>
      </view>
      
      <view class="result-meta">
        <view class="meta-item">
          <text class="meta-label">文件类型:</text>
          <text class="meta-value file-type">{{item.fileType || '未知'}}</text>
        </view>
        <view class="meta-item" wx:if="{{item.fileSize}}">
          <text class="meta-label">文件大小:</text>
          <text class="meta-value">{{item.fileSize}}</text>
        </view>
        <view class="meta-item" wx:if="{{item.source}}">
          <text class="meta-label">来源:</text>
          <text class="meta-value">{{item.source}}</text>
        </view>
        <view class="meta-item" wx:if="{{item.publishTime}}">
          <text class="meta-label">发布时间:</text>
          <text class="meta-value">{{item.publishTime}}</text>
        </view>
      </view>

      <view class="result-description" wx:if="{{item.description}}">
        {{item.description}}
      </view>

      <!-- 预览按钮 -->
      <view class="result-preview" wx:if="{{item.previewUrl}}">
        <button 
          class="btn btn-small btn-secondary"
          bindtap="previewFile"
          data-index="{{index}}"
          catchtap="true"
        >
          预览
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && results.length === 0 && searched}}">
    <view class="empty-icon">🔍</view>
    <view class="empty-text">
      没有找到相关文档<br/>
      请尝试调整搜索关键词或筛选条件
    </view>
    <button 
      class="btn btn-primary"
      bindtap="goBack"
    >
      重新搜索
    </button>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !loading}}">
    <button 
      class="btn btn-secondary"
      bindtap="loadMore"
      loading="{{loadingMore}}"
    >
      {{loadingMore ? '加载中...' : '加载更多'}}
    </button>
  </view>
</view>

<!-- 预览模态框 -->
<view class="preview-modal {{showPreview ? 'show' : ''}}" wx:if="{{previewFile}}">
  <view class="modal-mask" bindtap="closePreview"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">文件预览</text>
      <text class="modal-close" bindtap="closePreview">×</text>
    </view>
    <view class="modal-body">
      <web-view src="{{previewFile.previewUrl}}" wx:if="{{previewFile.previewUrl}}"></web-view>
      <view class="preview-placeholder" wx:else>
        <text>该文件暂不支持预览</text>
      </view>
    </view>
  </view>
</view>
