/* app.wxss */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 通用样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
}

.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-weight: 600;
  font-size: 32rpx;
  color: #333;
}

.card-body {
  padding: 30rpx;
}

/* 按钮样式 */
.btn {
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1rpx solid #dee2e6;
}

.btn-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.btn-small {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 24rpx 48rpx;
  font-size: 32rpx;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.input-field {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  transition: border-color 0.3s ease;
}

.input-field:focus {
  border-color: #667eea;
  outline: none;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  background: #f8f9fa;
  color: #6c757d;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin: 4rpx 8rpx 4rpx 0;
  border: 1rpx solid #dee2e6;
}

.tag.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 列表样式 */
.list-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: #f8f9fa;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.list-item-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.list-item-meta {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 60rpx;
  color: #999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* 进度条 */
.progress {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

/* 状态标签 */
.status-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-downloading {
  background: #d1ecf1;
  color: #0c5460;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-failed {
  background: #f8d7da;
  color: #721c24;
}
