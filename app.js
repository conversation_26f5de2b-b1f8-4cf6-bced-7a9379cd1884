// app.js
App({
  globalData: {
    searchHistory: [],
    downloadQueue: [],
    userSettings: {
      defaultEngine: 'baidu',
      defaultFileTypes: ['pdf', 'doc', 'docx'],
      downloadPath: ''
    }
  },

  onLaunch() {
    // 小程序启动时执行
    this.loadUserSettings();
    this.loadSearchHistory();
  },

  onShow() {
    // 小程序显示时执行
  },

  onHide() {
    // 小程序隐藏时执行
    this.saveUserSettings();
    this.saveSearchHistory();
  },

  // 加载用户设置
  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('userSettings');
      if (settings) {
        this.globalData.userSettings = { ...this.globalData.userSettings, ...settings };
      }
    } catch (e) {
      console.error('加载用户设置失败:', e);
    }
  },

  // 保存用户设置
  saveUserSettings() {
    try {
      wx.setStorageSync('userSettings', this.globalData.userSettings);
    } catch (e) {
      console.error('保存用户设置失败:', e);
    }
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory');
      if (history) {
        this.globalData.searchHistory = history;
      }
    } catch (e) {
      console.error('加载搜索历史失败:', e);
    }
  },

  // 保存搜索历史
  saveSearchHistory() {
    try {
      wx.setStorageSync('searchHistory', this.globalData.searchHistory);
    } catch (e) {
      console.error('保存搜索历史失败:', e);
    }
  },

  // 添加搜索历史
  addSearchHistory(query, engine, fileTypes, site) {
    const historyItem = {
      id: Date.now(),
      query,
      engine,
      fileTypes,
      site,
      timestamp: new Date().toISOString()
    };
    
    // 去重
    this.globalData.searchHistory = this.globalData.searchHistory.filter(
      item => !(item.query === query && item.engine === engine && item.site === site)
    );
    
    // 添加到开头
    this.globalData.searchHistory.unshift(historyItem);
    
    // 限制历史记录数量
    if (this.globalData.searchHistory.length > 50) {
      this.globalData.searchHistory = this.globalData.searchHistory.slice(0, 50);
    }
  },

  // 添加到下载队列
  addToDownloadQueue(file) {
    const downloadItem = {
      id: Date.now(),
      ...file,
      status: 'pending', // pending, downloading, completed, failed
      progress: 0,
      timestamp: new Date().toISOString()
    };
    
    this.globalData.downloadQueue.push(downloadItem);
    return downloadItem.id;
  },

  // 更新下载状态
  updateDownloadStatus(id, status, progress = 0) {
    const item = this.globalData.downloadQueue.find(item => item.id === id);
    if (item) {
      item.status = status;
      item.progress = progress;
    }
  }
});
