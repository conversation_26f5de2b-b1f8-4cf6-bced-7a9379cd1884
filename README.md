# 文档搜索下载工具 - 微信小程序版

一个高效易用的文档搜索下载工具，支持多搜索引擎、多文件类型，专为微信小程序环境设计。

## 功能特性

### 🔍 强大的搜索功能
- **多搜索引擎支持**：百度、Bing、Google
- **高级搜索语法**：
  - `filetype:` 文件类型筛选（pdf, doc, docx, ppt, xls等）
  - `site:` 站点限制（如 site:weixin.qq.com 搜索公众号文章）
  - 时间范围筛选
- **智能搜索历史**：自动保存搜索记录，支持快速重用

### 📥 便捷的下载管理
- **批量下载**：支持选择多个文件同时下载
- **下载队列**：实时显示下载进度和状态
- **断点续传**：支持暂停、继续、重试下载
- **智能分类**：按文件类型和状态自动分类

### 🎨 优雅的用户界面
- **响应式设计**：适配不同屏幕尺寸
- **直观操作**：简洁明了的交互设计
- **实时反馈**：下载进度、状态变化实时更新

### ⚡ 高性能优化
- **纯前端实现**：无需后端服务，利用用户设备资源
- **异步处理**：搜索和下载不阻塞界面操作
- **内存优化**：智能缓存和垃圾回收

## 项目结构

```
wing/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── pages/                # 页面目录
│   ├── search/           # 搜索页面
│   │   ├── search.js
│   │   ├── search.wxml
│   │   └── search.wxss
│   ├── results/          # 搜索结果页面
│   │   ├── results.js
│   │   ├── results.wxml
│   │   └── results.wxss
│   └── downloads/        # 下载管理页面
│       ├── downloads.js
│       ├── downloads.wxml
│       └── downloads.wxss
├── utils/                # 工具类目录
│   ├── searchEngine.js   # 搜索引擎工具
│   └── downloadManager.js # 下载管理工具
└── images/               # 图片资源目录
```

## 快速开始

### 1. 环境准备
- 微信开发者工具
- 微信小程序开发账号

### 2. 项目导入
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录
4. 填写AppID（测试可使用测试号）

### 3. 运行项目
1. 点击"编译"按钮
2. 在模拟器中预览效果
3. 可使用真机调试测试完整功能

## 使用说明

### 搜索文档
1. 在搜索页面输入关键词
2. 选择搜索引擎（百度/Bing/Google）
3. 选择文件类型（PDF、Word、PPT等）
4. 可选：设置站点限制和时间范围
5. 点击"开始搜索"

### 下载管理
1. 在搜索结果页面选择要下载的文件
2. 支持单个下载或批量下载
3. 在下载页面查看下载进度
4. 支持暂停、继续、重试操作

### 快捷功能
- **搜索历史**：自动保存最近搜索，点击可快速重用
- **快捷搜索**：预设常用搜索场景（学术论文、行业报告等）
- **文件预览**：支持在线预览部分文件类型

## 技术实现

### 前端技术
- **框架**：微信小程序原生框架
- **样式**：WXSS + CSS3动画
- **数据管理**：小程序全局数据 + 本地存储

### 核心功能
- **搜索引擎集成**：通过构建搜索URL实现多引擎支持
- **文件下载**：使用wx.downloadFile API
- **进度管理**：实时监听下载进度和状态
- **数据持久化**：使用wx.setStorageSync保存用户数据

### 性能优化
- **懒加载**：搜索结果分页加载
- **缓存策略**：搜索历史和用户设置本地缓存
- **异步处理**：所有网络请求异步执行
- **内存管理**：及时清理无用数据

## 注意事项

### 使用限制
1. **网络依赖**：需要网络连接进行搜索和下载
2. **文件大小**：受微信小程序文件大小限制
3. **下载路径**：文件保存在小程序沙盒环境中
4. **跨域限制**：部分网站可能存在跨域访问限制

### 版权声明
- 本工具仅用于合法文档的搜索和下载
- 用户需遵守相关版权法律法规
- 下载的文件仅供个人学习和研究使用

### 隐私保护
- 搜索历史仅保存在用户设备本地
- 不收集用户个人信息
- 不上传用户搜索和下载数据

## 开发计划

### 近期计划
- [ ] 添加更多搜索引擎支持
- [ ] 优化搜索结果解析算法
- [ ] 增加文件分类和标签功能
- [ ] 支持云端同步搜索历史

### 长期计划
- [ ] 集成AI智能推荐
- [ ] 支持OCR文字识别
- [ ] 添加文档内容搜索
- [ ] 开发桌面版和Web版

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境
1. Fork本项目
2. 创建功能分支
3. 提交代码变更
4. 发起Pull Request

### 代码规范
- 使用ES6+语法
- 遵循微信小程序开发规范
- 添加必要的注释和文档

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者邮箱

---

**让文档搜索变得更简单！** 🚀
