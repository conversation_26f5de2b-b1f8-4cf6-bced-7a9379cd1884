#!/usr/bin/env python3
"""
简单的HTTP服务器，用于本地测试Web版本
使用方法：python3 server.py
然后在浏览器中访问 http://localhost:8080
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

# 设置端口
PORT = 8080

# 获取当前脚本所在目录
CURRENT_DIR = Path(__file__).parent

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(CURRENT_DIR), **kwargs)
    
    def end_headers(self):
        # 添加CORS头，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()

def main():
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 服务器启动成功!")
            print(f"📱 Web版本地址: http://localhost:{PORT}")
            print(f"🌐 网络地址: http://0.0.0.0:{PORT}")
            print(f"📁 服务目录: {CURRENT_DIR}")
            print(f"⚡ 按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            # 检查必要文件是否存在
            required_files = ['index.html', 'styles.css', 'script.js']
            missing_files = []
            
            for file in required_files:
                if not (CURRENT_DIR / file).exists():
                    missing_files.append(file)
            
            if missing_files:
                print(f"⚠️  警告: 以下文件不存在: {', '.join(missing_files)}")
            else:
                print("✅ 所有必要文件都存在")
            
            print("-" * 50)
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用，请尝试其他端口")
            print(f"💡 可以修改脚本中的 PORT 变量")
        else:
            print(f"❌ 启动服务器失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
