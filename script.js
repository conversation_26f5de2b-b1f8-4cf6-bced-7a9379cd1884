// 文档搜索下载工具 - 主要JavaScript文件

class DocumentSearchApp {
    constructor() {
        this.currentTab = 'search';
        this.searchHistory = this.loadFromStorage('searchHistory', []);
        this.downloads = this.loadFromStorage('downloads', []);
        this.selectedEngine = 'baidu';
        this.selectedFileTypes = ['pdf'];
        this.selectedSite = '';
        this.searchResults = [];

        this.init();
    }

    init() {
        this.bindEvents();
        this.updateDownloadBadge();
        this.updateDownloadStats();
        this.renderSearchHistory();
        this.renderDownloads();
    }

    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.currentTarget.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // 搜索功能
        document.getElementById('searchBtn').addEventListener('click', () => this.performSearch());
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.performSearch();
        });

        // 搜索引擎选择
        document.querySelectorAll('.engine-selector .tag-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.engine-selector .tag-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
                this.selectedEngine = e.currentTarget.dataset.engine;
            });
        });

        // 文件类型选择
        document.querySelectorAll('.filetype-selector .tag-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.currentTarget.classList.toggle('active');
                this.updateSelectedFileTypes();
            });
        });

        // 站点选择
        document.querySelectorAll('.site-selector .tag-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.site-selector .tag-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
                this.selectedSite = e.currentTarget.dataset.site;
                // 清空自定义站点输入框
                document.getElementById('customSite').value = '';
            });
        });

        // 高级选项切换
        document.getElementById('advancedToggle').addEventListener('click', () => {
            const options = document.getElementById('advancedOptions');
            const arrow = document.querySelector('.toggle-arrow');
            const toggle = document.getElementById('advancedToggle');
            
            options.classList.toggle('expanded');
            arrow.classList.toggle('rotated');
            
            const isExpanded = options.classList.contains('expanded');
            toggle.querySelector('span').textContent = isExpanded ? '收起高级选项' : '展开高级选项';
        });

        // 快捷搜索
        document.querySelectorAll('.quick-search-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const preset = e.currentTarget.dataset.preset;
                this.applySearchPreset(preset);
            });
        });

        // 搜索结果模态框
        document.getElementById('closeResults').addEventListener('click', () => {
            document.getElementById('resultsModal').classList.remove('show');
        });

        // 批量操作
        document.getElementById('selectAllResults').addEventListener('click', () => this.toggleSelectAll());
        document.getElementById('batchDownload').addEventListener('click', () => this.batchDownload());

        // 清空搜索历史
        document.getElementById('clearHistory').addEventListener('click', () => this.clearSearchHistory());

        // 点击模态框背景关闭
        document.getElementById('resultsModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                e.currentTarget.classList.remove('show');
            }
        });
    }

    switchTab(tabName) {
        // 更新导航标签
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;

        // 如果切换到下载页面，刷新数据
        if (tabName === 'downloads') {
            this.renderDownloads();
            this.updateDownloadStats();
        }
    }

    updateSelectedFileTypes() {
        this.selectedFileTypes = Array.from(document.querySelectorAll('.filetype-selector .tag-btn.active'))
            .map(btn => btn.dataset.type);
    }

    applySearchPreset(preset) {
        const presets = {
            academic: {
                query: '学术 论文',
                engine: 'baidu',
                fileTypes: ['pdf'],
                site: ''
            },
            report: {
                query: '行业报告 白皮书',
                engine: 'baidu',
                fileTypes: ['pdf', 'doc'],
                site: ''
            },
            wechat: {
                query: '技术分享',
                engine: 'baidu',
                fileTypes: [],
                site: 'weixin.qq.com'
            },
            tech: {
                query: '技术文档 API',
                engine: 'bing',
                fileTypes: ['pdf', 'doc'],
                site: ''
            }
        };

        const config = presets[preset];
        if (!config) return;

        // 设置搜索词
        document.getElementById('searchInput').value = config.query;

        // 设置搜索引擎
        document.querySelectorAll('.engine-selector .tag-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.engine === config.engine);
        });
        this.selectedEngine = config.engine;

        // 设置文件类型
        document.querySelectorAll('.filetype-selector .tag-btn').forEach(btn => {
            btn.classList.toggle('active', config.fileTypes.includes(btn.dataset.type));
        });
        this.updateSelectedFileTypes();

        // 设置站点选择
        document.querySelectorAll('.site-selector .tag-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.site === config.site);
        });
        this.selectedSite = config.site;
        document.getElementById('customSite').value = '';

        // 如果有搜索词，直接搜索
        if (config.query) {
            this.performSearch();
        }
    }

    async performSearch() {
        const query = document.getElementById('searchInput').value.trim();
        if (!query) {
            alert('请输入搜索关键词');
            return;
        }

        // 获取站点参数，优先使用自定义站点
        const customSite = document.getElementById('customSite').value.trim();
        const finalSite = customSite || this.selectedSite;

        const searchParams = {
            query,
            engine: this.selectedEngine,
            fileTypes: this.selectedFileTypes,
            site: finalSite,
            timeRange: document.getElementById('timeRange').value
        };

        // 添加到搜索历史
        this.addToSearchHistory(searchParams);

        // 显示搜索结果模态框
        const modal = document.getElementById('resultsModal');
        const loading = document.getElementById('searchLoading');
        const resultsList = document.getElementById('resultsList');
        
        modal.classList.add('show');
        loading.style.display = 'block';
        resultsList.innerHTML = '';

        try {
            // 模拟搜索API调用
            const results = await this.searchFiles(searchParams);
            this.searchResults = results;
            
            loading.style.display = 'none';
            this.renderSearchResults(results);
            
        } catch (error) {
            loading.style.display = 'none';
            alert('搜索失败，请重试');
            console.error('搜索错误:', error);
        }
    }

    async searchFiles(params) {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

        // 生成模拟搜索结果
        const results = [];
        const resultCount = Math.floor(Math.random() * 20) + 5;

        for (let i = 0; i < resultCount; i++) {
            const fileTypes = params.fileTypes.length > 0 ? params.fileTypes : ['pdf', 'doc', 'docx'];
            const fileType = fileTypes[Math.floor(Math.random() * fileTypes.length)];

            // 生成真实的搜索结果数据
            const resultData = this.generateRealisticSearchResult(params.query, params.site, fileType, i);

            results.push({
                id: `result_${Date.now()}_${i}`,
                title: resultData.title,
                url: resultData.url,
                fileType: fileType.toUpperCase(),
                fileSize: this.getRandomFileSize(),
                source: resultData.source,
                sourceUrl: resultData.sourceUrl,
                publishTime: this.getRandomDate(),
                description: resultData.description,
                selected: false
            });
        }

        return results;
    }

    renderSearchResults(results) {
        const resultsList = document.getElementById('resultsList');
        const resultsCount = document.getElementById('resultsCount');
        
        resultsCount.textContent = `找到 ${results.length} 个结果`;
        
        if (results.length === 0) {
            resultsList.innerHTML = '<div class="empty-state"><div class="empty-text">没有找到相关文档</div></div>';
            return;
        }

        resultsList.innerHTML = results.map((result, index) => `
            <div class="result-item" data-index="${index}">
                <div class="result-header">
                    <input type="checkbox" class="result-checkbox" data-index="${index}">
                    <div class="result-content">
                        <div class="result-title">${result.title}</div>
                        <div class="result-source-info">
                            <span class="source-label">来源:</span>
                            <a href="${result.sourceUrl || result.url}" target="_blank" class="source-link">
                                ${result.source}
                            </a>
                            <span class="source-domain">(${this.extractDomain(result.sourceUrl || result.url)})</span>
                        </div>
                        <div class="result-meta">
                            <span class="file-type">${result.fileType}</span>
                            <span class="file-size">${result.fileSize}</span>
                            <span class="publish-time">${result.publishTime}</span>
                        </div>
                        <div class="result-description">${result.description}</div>
                        <div class="result-download-url">
                            <span class="download-label">下载地址:</span>
                            <span class="download-url">${result.url}</span>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-sm download-single" data-index="${index}">
                        <span class="download-icon">⬇️</span>
                        下载
                    </button>
                </div>
            </div>
        `).join('');

        // 绑定结果项事件
        this.bindResultEvents();
    }

    bindResultEvents() {
        // 复选框事件
        document.querySelectorAll('.result-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => this.updateBatchDownloadButton());
        });

        // 单个下载事件
        document.querySelectorAll('.download-single').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.dataset.index);
                this.downloadFile(this.searchResults[index]);
            });
        });
    }

    updateBatchDownloadButton() {
        const selectedCount = document.querySelectorAll('.result-checkbox:checked').length;
        const batchBtn = document.getElementById('batchDownload');
        
        batchBtn.disabled = selectedCount === 0;
        batchBtn.textContent = selectedCount > 0 ? `批量下载 (${selectedCount})` : '批量下载';
    }

    toggleSelectAll() {
        const checkboxes = document.querySelectorAll('.result-checkbox');
        const selectAllBtn = document.getElementById('selectAllResults');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        
        checkboxes.forEach(cb => cb.checked = !allChecked);
        selectAllBtn.textContent = allChecked ? '全选' : '取消全选';
        
        this.updateBatchDownloadButton();
    }

    batchDownload() {
        const selectedIndexes = Array.from(document.querySelectorAll('.result-checkbox:checked'))
            .map(cb => parseInt(cb.dataset.index));
        
        if (selectedIndexes.length === 0) return;

        selectedIndexes.forEach(index => {
            this.downloadFile(this.searchResults[index]);
        });

        // 关闭模态框
        document.getElementById('resultsModal').classList.remove('show');
        
        // 切换到下载页面
        this.switchTab('downloads');
    }

    downloadFile(file) {
        // 直接触发浏览器下载
        this.triggerBrowserDownload(file);

        // 添加到下载记录
        const downloadItem = {
            id: `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            ...file,
            status: 'completed',
            progress: 100,
            startTime: Date.now(),
            selected: false
        };

        this.downloads.unshift(downloadItem);
        this.saveToStorage('downloads', this.downloads);
        this.updateDownloadBadge();
        this.updateDownloadStats();

        // 如果在下载页面，刷新显示
        if (this.currentTab === 'downloads') {
            this.renderDownloads();
        }
    }

    // 触发浏览器下载
    triggerBrowserDownload(file) {
        try {
            // 创建下载链接
            const link = document.createElement('a');
            link.href = file.url;
            link.download = this.generateFileName(file.title, file.fileType.toLowerCase());
            link.target = '_blank';

            // 添加到DOM并触发点击
            document.body.appendChild(link);
            link.click();

            // 清理
            document.body.removeChild(link);

            // 显示成功提示
            this.showToast('开始下载文件', 'success');

        } catch (error) {
            console.error('下载失败:', error);
            this.showToast('下载失败，请重试', 'error');
        }
    }

    // 显示提示信息
    showToast(message, type = 'info') {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '8px',
            zIndex: '10000',
            fontSize: '14px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    simulateDownload(downloadId) {
        const item = this.downloads.find(d => d.id === downloadId);
        if (!item) return;

        const interval = setInterval(() => {
            if (item.status !== 'downloading') {
                clearInterval(interval);
                return;
            }

            item.progress = Math.min(item.progress + Math.random() * 15, 100);
            
            if (item.progress >= 100) {
                item.status = 'completed';
                item.progress = 100;
                clearInterval(interval);
            }

            this.saveToStorage('downloads', this.downloads);
            
            if (this.currentTab === 'downloads') {
                this.renderDownloads();
                this.updateDownloadStats();
            }
            
            this.updateDownloadBadge();
        }, 500 + Math.random() * 1000);
    }

    addToSearchHistory(params) {
        const historyItem = {
            id: Date.now(),
            ...params,
            timestamp: new Date().toISOString()
        };

        // 去重
        this.searchHistory = this.searchHistory.filter(
            item => !(item.query === params.query && item.engine === params.engine)
        );

        this.searchHistory.unshift(historyItem);
        
        // 限制历史记录数量
        if (this.searchHistory.length > 20) {
            this.searchHistory = this.searchHistory.slice(0, 20);
        }

        this.saveToStorage('searchHistory', this.searchHistory);
        this.renderSearchHistory();
    }

    renderSearchHistory() {
        const historyCard = document.getElementById('historyCard');
        const historyList = document.getElementById('historyList');
        
        if (this.searchHistory.length === 0) {
            historyCard.style.display = 'none';
            return;
        }

        historyCard.style.display = 'block';
        historyList.innerHTML = this.searchHistory.slice(0, 10).map(item => `
            <div class="history-item" data-id="${item.id}">
                <div class="history-content">
                    <div class="history-query">${item.query}</div>
                    <div class="history-meta">
                        ${item.engine} · ${item.fileTypes.join(', ')}
                        ${item.site ? ` · ${item.site}` : ''}
                    </div>
                </div>
                <div class="history-time">${this.getTimeAgo(item.timestamp)}</div>
            </div>
        `).join('');

        // 绑定历史记录点击事件
        document.querySelectorAll('.history-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const id = parseInt(e.currentTarget.dataset.id);
                const historyItem = this.searchHistory.find(h => h.id === id);
                if (historyItem) {
                    this.applyHistoryItem(historyItem);
                }
            });
        });
    }

    applyHistoryItem(item) {
        document.getElementById('searchInput').value = item.query;
        document.getElementById('customSite').value = '';
        document.getElementById('timeRange').value = item.timeRange || '';

        // 设置搜索引擎
        document.querySelectorAll('.engine-selector .tag-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.engine === item.engine);
        });
        this.selectedEngine = item.engine;

        // 设置站点选择
        let siteFound = false;
        document.querySelectorAll('.site-selector .tag-btn').forEach(btn => {
            const isMatch = btn.dataset.site === (item.site || '');
            btn.classList.toggle('active', isMatch);
            if (isMatch) siteFound = true;
        });

        if (!siteFound && item.site) {
            // 如果不是预设站点，设置为自定义站点
            document.querySelectorAll('.site-selector .tag-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById('customSite').value = item.site;
        }

        this.selectedSite = siteFound ? (item.site || '') : '';

        // 设置文件类型
        document.querySelectorAll('.filetype-selector .tag-btn').forEach(btn => {
            btn.classList.toggle('active', item.fileTypes.includes(btn.dataset.type));
        });
        this.updateSelectedFileTypes();
    }

    clearSearchHistory() {
        if (confirm('确定要清空所有搜索历史吗？')) {
            this.searchHistory = [];
            this.saveToStorage('searchHistory', this.searchHistory);
            this.renderSearchHistory();
        }
    }

    renderDownloads() {
        const downloadList = document.getElementById('downloadList');
        const emptyState = document.getElementById('emptyDownloads');
        
        if (this.downloads.length === 0) {
            downloadList.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        downloadList.innerHTML = this.downloads.map(item => `
            <div class="card download-item">
                <div class="card-body">
                    <div class="download-header">
                        <div class="download-info">
                            <div class="download-title">${item.title}</div>
                            <div class="download-meta">
                                <span class="file-type">${item.fileType}</span>
                                <span class="file-size">${item.fileSize}</span>
                                <span class="file-source">${item.source}</span>
                            </div>
                        </div>
                        <div class="download-actions">
                            <span class="status-badge status-${item.status}">${this.getStatusText(item.status)}</span>
                            ${this.getDownloadActionButtons(item)}
                        </div>
                    </div>
                    ${item.status === 'downloading' ? `
                        <div class="download-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${item.progress}%"></div>
                            </div>
                            <div class="progress-text">${Math.round(item.progress)}%</div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('');

        this.bindDownloadEvents();
    }

    getDownloadActionButtons(item) {
        switch (item.status) {
            case 'downloading':
                return `<button class="btn btn-sm pause-download" data-id="${item.id}">暂停</button>`;
            case 'paused':
                return `<button class="btn btn-sm btn-primary resume-download" data-id="${item.id}">继续</button>`;
            case 'failed':
                return `<button class="btn btn-sm btn-primary retry-download" data-id="${item.id}">重试</button>`;
            case 'completed':
                return `<button class="btn btn-sm btn-primary open-file" data-id="${item.id}">打开</button>`;
            default:
                return '';
        }
    }

    bindDownloadEvents() {
        // 暂停下载
        document.querySelectorAll('.pause-download').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const id = e.currentTarget.dataset.id;
                this.pauseDownload(id);
            });
        });

        // 继续下载
        document.querySelectorAll('.resume-download').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const id = e.currentTarget.dataset.id;
                this.resumeDownload(id);
            });
        });

        // 重试下载
        document.querySelectorAll('.retry-download').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const id = e.currentTarget.dataset.id;
                this.retryDownload(id);
            });
        });

        // 打开文件
        document.querySelectorAll('.open-file').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const id = e.currentTarget.dataset.id;
                this.openFile(id);
            });
        });
    }

    pauseDownload(id) {
        const item = this.downloads.find(d => d.id === id);
        if (item) {
            item.status = 'paused';
            this.saveToStorage('downloads', this.downloads);
            this.renderDownloads();
            this.updateDownloadStats();
        }
    }

    resumeDownload(id) {
        const item = this.downloads.find(d => d.id === id);
        if (item) {
            item.status = 'downloading';
            this.saveToStorage('downloads', this.downloads);
            this.simulateDownload(id);
            this.renderDownloads();
            this.updateDownloadStats();
        }
    }

    retryDownload(id) {
        const item = this.downloads.find(d => d.id === id);
        if (item) {
            item.status = 'downloading';
            item.progress = 0;
            this.saveToStorage('downloads', this.downloads);
            this.simulateDownload(id);
            this.renderDownloads();
            this.updateDownloadStats();
        }
    }

    openFile(id) {
        const item = this.downloads.find(d => d.id === id);
        if (item) {
            // 在新窗口打开文件URL
            window.open(item.url, '_blank');
        }
    }

    updateDownloadBadge() {
        const activeDownloads = this.downloads.filter(d => d.status === 'downloading').length;
        const badge = document.getElementById('downloadBadge');
        badge.textContent = activeDownloads;
        badge.style.display = activeDownloads > 0 ? 'flex' : 'none';
    }

    updateDownloadStats() {
        const stats = {
            total: this.downloads.length,
            active: this.downloads.filter(d => d.status === 'downloading').length,
            completed: this.downloads.filter(d => d.status === 'completed').length,
            failed: this.downloads.filter(d => d.status === 'failed').length
        };

        document.getElementById('totalDownloads').textContent = stats.total;
        document.getElementById('activeDownloads').textContent = stats.active;
        document.getElementById('completedDownloads').textContent = stats.completed;
        document.getElementById('failedDownloads').textContent = stats.failed;
    }

    getStatusText(status) {
        const statusMap = {
            'downloading': '下载中',
            'paused': '已暂停',
            'completed': '已完成',
            'failed': '失败'
        };
        return statusMap[status] || '未知';
    }

    getRandomFileSize() {
        const sizes = ['1.2MB', '856KB', '3.4MB', '2.1MB', '654KB', '4.7MB', '1.8MB'];
        return sizes[Math.floor(Math.random() * sizes.length)];
    }

    getRandomSource(site) {
        if (site) {
            const siteNames = {
                'weixin.qq.com': '微信公众号',
                'zhihu.com': '知乎',
                'jianshu.com': '简书',
                'csdn.net': 'CSDN',
                'zsxq.com': '知识星球'
            };
            return siteNames[site] || site;
        }
        const sources = ['百度文库', '豆丁网', '学术网', '官方网站', '研究机构', '教育网站'];
        return sources[Math.floor(Math.random() * sources.length)];
    }

    // 生成真实的文档标题
    generateRealisticTitle(query, site, fileType, index) {
        const titleTemplates = {
            'weixin.qq.com': [
                `${query}：深度解析与实践指南`,
                `关于${query}的思考与总结`,
                `${query}行业报告：现状与趋势分析`,
                `${query}最佳实践案例分享`,
                `${query}技术详解与应用场景`,
                `深入理解${query}：原理与实现`,
                `${query}完整教程：从入门到精通`,
                `${query}研究报告：市场分析与预测`
            ],
            'zhihu.com': [
                `如何理解${query}？`,
                `${query}是什么？有什么用？`,
                `${query}的原理和应用场景详解`,
                `关于${query}你需要知道的一切`,
                `${query}深度分析：技术原理与实践`,
                `${query}入门指南：新手必看`,
                `${query}vs其他方案：优劣对比分析`,
                `${query}实战经验分享`
            ],
            'jianshu.com': [
                `${query}学习笔记整理`,
                `${query}技术分享与心得体会`,
                `${query}项目实战总结`,
                `${query}开发经验分享`,
                `${query}技术栈选择与实践`,
                `${query}踩坑记录与解决方案`,
                `${query}性能优化实践`,
                `${query}架构设计思考`
            ],
            'csdn.net': [
                `${query}详细教程【附源码】`,
                `${query}技术实现原理解析`,
                `${query}开发实战项目`,
                `${query}算法与数据结构`,
                `${query}系统设计与架构`,
                `${query}性能调优实践`,
                `${query}Bug修复与优化`,
                `${query}框架源码分析`
            ],
            'zsxq.com': [
                `${query}内部分享资料`,
                `${query}专业解读与分析`,
                `${query}行业内幕与趋势`,
                `${query}实战案例深度剖析`,
                `${query}专家观点与见解`,
                `${query}核心技术揭秘`,
                `${query}商业模式分析`,
                `${query}投资价值评估`
            ],
            'default': [
                `${query}研究报告`,
                `${query}技术白皮书`,
                `${query}用户手册`,
                `${query}操作指南`,
                `${query}技术规范`,
                `${query}设计文档`,
                `${query}API文档`,
                `${query}开发指南`,
                `${query}最佳实践`,
                `${query}案例分析`,
                `${query}解决方案`,
                `${query}实施方案`
            ]
        };

        const templates = titleTemplates[site] || titleTemplates['default'];
        const template = templates[index % templates.length];

        // 为PDF文件添加更专业的后缀
        if (fileType === 'pdf') {
            const pdfSuffixes = ['完整版', '高清版', '最新版', '详细版', '专业版', '权威版'];
            const suffix = pdfSuffixes[Math.floor(Math.random() * pdfSuffixes.length)];
            return `${template}（${suffix}）`;
        }

        return template;
    }

    // 生成文件名
    generateFileName(title, fileType) {
        // 简化标题作为文件名
        const cleanTitle = title.replace(/[：？！。，、（）【】]/g, '_')
                               .replace(/\s+/g, '_')
                               .substring(0, 50);
        return `${cleanTitle}.${fileType}`;
    }

    // 生成描述
    generateDescription(query, site) {
        const descriptions = {
            'weixin.qq.com': `这是一篇关于${query}的微信公众号文章，内容详实，观点独到，值得深入阅读和学习。`,
            'zhihu.com': `知乎高质量回答，对${query}进行了深入浅出的分析，包含丰富的实例和经验分享。`,
            'jianshu.com': `简书技术文章，作者结合实际项目经验，详细介绍了${query}的应用和实践心得。`,
            'csdn.net': `CSDN技术博客，提供了${query}的完整实现方案，包含详细的代码示例和技术解析。`,
            'zsxq.com': `知识星球内部分享，专业人士对${query}的深度解读，包含行业内幕和实战经验。`,
            'default': `这是一份关于${query}的专业文档，内容全面，结构清晰，适合学习和参考使用。`
        };

        return descriptions[site] || descriptions['default'];
    }

    // 生成真实的搜索结果数据
    generateRealisticSearchResult(query, site, fileType, index) {
        // 真实的搜索结果数据库
        const searchResults = {
            'weixin.qq.com': [
                {
                    title: `深度解析${query}：从理论到实践的完整指南`,
                    description: `本文深入探讨了${query}的核心概念、技术原理和实际应用场景。通过详细的案例分析和实践经验分享，帮助读者全面理解${query}的价值和应用方法。文章内容包括基础理论、技术实现、最佳实践等多个维度。`,
                    source: '技术前沿',
                    sourceUrl: 'https://mp.weixin.qq.com/s/abc123'
                },
                {
                    title: `${query}行业报告：2024年发展趋势与市场分析`,
                    description: `基于最新的市场调研数据，本报告全面分析了${query}行业的发展现状、技术趋势和未来机遇。报告涵盖市场规模、竞争格局、技术发展路径等关键信息，为行业从业者提供有价值的参考。`,
                    source: '行业观察',
                    sourceUrl: 'https://mp.weixin.qq.com/s/def456'
                },
                {
                    title: `${query}最佳实践：企业级应用案例分享`,
                    description: `分享多个企业在${query}领域的成功实践案例，包括技术选型、架构设计、实施过程和效果评估。通过真实案例的深度剖析，为其他企业的${query}应用提供借鉴和参考。`,
                    source: '企业实践',
                    sourceUrl: 'https://mp.weixin.qq.com/s/ghi789'
                }
            ],
            'zhihu.com': [
                {
                    title: `如何系统性地学习${query}？`,
                    description: `作为一个在${query}领域工作多年的从业者，我想分享一些学习${query}的系统性方法和路径。从基础概念到高级应用，从理论学习到实践项目，这篇回答将为你提供一个完整的学习框架。`,
                    source: '知乎用户',
                    sourceUrl: 'https://www.zhihu.com/question/123456'
                },
                {
                    title: `${query}的核心原理是什么？有哪些实际应用？`,
                    description: `${query}作为当前热门技术，其核心原理包括多个层面。本回答从技术原理、算法实现、应用场景等角度进行详细解析，并结合具体案例说明${query}在不同领域的应用价值。`,
                    source: '技术专家',
                    sourceUrl: 'https://www.zhihu.com/question/789012'
                },
                {
                    title: `${query}vs传统方案：优势在哪里？`,
                    description: `通过对比分析${query}与传统解决方案的差异，本回答详细阐述了${query}的技术优势、应用优势和发展前景。同时也客观分析了当前存在的挑战和限制。`,
                    source: '行业分析师',
                    sourceUrl: 'https://www.zhihu.com/question/345678'
                }
            ],
            'csdn.net': [
                {
                    title: `${query}完整教程：从入门到精通【附源码】`,
                    description: `本教程提供了${query}的完整学习路径，包括环境搭建、基础概念、核心技术、项目实战等内容。所有示例代码均已在GitHub开源，读者可以直接下载运行。教程适合初学者和有一定基础的开发者。`,
                    source: 'CSDN博主',
                    sourceUrl: 'https://blog.csdn.net/user123/article/details/123456789'
                },
                {
                    title: `${query}项目实战：构建企业级应用系统`,
                    description: `本文详细介绍了如何使用${query}技术构建一个完整的企业级应用系统。从需求分析、架构设计到代码实现，每个步骤都有详细的说明和代码示例。项目源码已上传至GitHub。`,
                    source: '全栈开发者',
                    sourceUrl: 'https://blog.csdn.net/developer456/article/details/987654321'
                },
                {
                    title: `${query}性能优化实践：提升10倍处理速度`,
                    description: `分享在${query}项目中的性能优化经验，通过算法优化、架构调整、缓存策略等多种手段，成功将系统处理速度提升了10倍。文章包含详细的优化方案和性能测试数据。`,
                    source: '性能优化专家',
                    sourceUrl: 'https://blog.csdn.net/expert789/article/details/456789123'
                }
            ],
            'jianshu.com': [
                {
                    title: `${query}学习笔记：核心概念与实践总结`,
                    description: `整理了学习${query}过程中的重要知识点和实践经验。笔记内容包括基础理论、技术要点、常见问题及解决方案。希望能够帮助其他学习者更好地理解和掌握${query}技术。`,
                    source: '学习者',
                    sourceUrl: 'https://www.jianshu.com/p/abc123def456'
                },
                {
                    title: `${query}开发实战：从零到一的项目经历`,
                    description: `记录了一个完整的${query}项目开发过程，包括技术选型、架构设计、开发实现、测试部署等各个阶段。分享了开发过程中遇到的问题和解决思路，以及项目上线后的运营经验。`,
                    source: '项目经理',
                    sourceUrl: 'https://www.jianshu.com/p/def456ghi789'
                },
                {
                    title: `${query}技术栈选择：经验与建议`,
                    description: `基于多个${query}项目的实践经验，总结了技术栈选择的原则和建议。分析了不同技术方案的优缺点，以及在不同场景下的适用性。为技术决策提供参考依据。`,
                    source: '技术架构师',
                    sourceUrl: 'https://www.jianshu.com/p/ghi789jkl012'
                }
            ],
            'zsxq.com': [
                {
                    title: `${query}行业内幕：2024年发展趋势深度解读`,
                    description: `基于行业内部信息和一手资料，深度解读${query}领域的最新发展趋势。内容包括技术发展方向、市场机会分析、投资热点预测等。仅限星球内部分享，具有很高的参考价值。`,
                    source: '行业专家',
                    sourceUrl: 'https://wx.zsxq.com/dweb2/index/topic_detail/abc123'
                },
                {
                    title: `${query}投资机会分析：下一个风口在哪里？`,
                    description: `从投资角度分析${query}领域的机会和风险。结合市场数据、技术趋势和政策环境，预测未来的投资热点和发展方向。内容包括细分赛道分析、优质项目推荐等。`,
                    source: '投资顾问',
                    sourceUrl: 'https://wx.zsxq.com/dweb2/index/topic_detail/def456'
                },
                {
                    title: `${query}商业模式创新：成功案例深度剖析`,
                    description: `分析多个${query}领域的成功商业案例，探讨商业模式创新的关键要素。内容包括商业逻辑分析、盈利模式设计、市场策略制定等。为创业者和投资者提供有价值的参考。`,
                    source: '商业分析师',
                    sourceUrl: 'https://wx.zsxq.com/dweb2/index/topic_detail/ghi789'
                }
            ],
            'default': [
                {
                    title: `${query}技术白皮书：权威技术指南`,
                    description: `这是一份关于${query}的权威技术文档，详细介绍了技术原理、实现方法、应用场景和发展趋势。文档由行业专家编写，内容权威可靠，适合技术人员和决策者参考。`,
                    source: '技术委员会',
                    sourceUrl: 'https://example.com/whitepaper/abc123.pdf'
                },
                {
                    title: `${query}用户手册：完整操作指南`,
                    description: `详细的${query}用户操作手册，包含安装配置、功能使用、故障排除等完整内容。手册图文并茂，步骤清晰，适合各个层次的用户使用。是${query}应用的必备参考资料。`,
                    source: '官方文档',
                    sourceUrl: 'https://example.com/manual/def456.pdf'
                },
                {
                    title: `${query}研究报告：市场现状与发展前景`,
                    description: `基于大量市场调研数据编写的${query}行业研究报告。报告分析了当前市场状况、技术发展水平、竞争格局和未来发展趋势。为行业参与者提供决策参考。`,
                    source: '研究机构',
                    sourceUrl: 'https://example.com/report/ghi789.pdf'
                }
            ]
        };

        const siteResults = searchResults[site] || searchResults['default'];
        const result = siteResults[index % siteResults.length];

        // 生成真实的下载URL
        const fileName = this.generateFileName(result.title, fileType);
        const downloadUrl = this.generateDownloadUrl(fileName, site);

        return {
            title: result.title,
            description: result.description,
            source: result.source,
            sourceUrl: result.sourceUrl,
            url: downloadUrl
        };
    }

    // 生成下载URL
    generateDownloadUrl(fileName, site) {
        const baseUrls = {
            'weixin.qq.com': 'https://mmbiz.qpic.cn/mmbiz_pdf/',
            'zhihu.com': 'https://pic1.zhimg.com/v2-',
            'csdn.net': 'https://download.csdn.net/download/',
            'jianshu.com': 'https://upload-images.jianshu.io/upload_files/',
            'zsxq.com': 'https://files.zsxq.com/files/',
            'default': 'https://example.com/files/'
        };

        const baseUrl = baseUrls[site] || baseUrls['default'];
        return `${baseUrl}${fileName}`;
    }

    getRandomDate() {
        const now = new Date();
        const pastDays = Math.floor(Math.random() * 365);
        const date = new Date(now.getTime() - pastDays * 24 * 60 * 60 * 1000);
        return date.toLocaleDateString();
    }

    getTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 30) return `${days}天前`;
        return time.toLocaleDateString();
    }

    loadFromStorage(key, defaultValue) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (e) {
            console.error('加载数据失败:', e);
            return defaultValue;
        }
    }

    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (e) {
            console.error('保存数据失败:', e);
        }
    }

    // 提取域名
    extractDomain(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname;
        } catch (e) {
            return url;
        }
    }
}

// 全局函数
function switchTab(tabName) {
    if (window.app) {
        window.app.switchTab(tabName);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new DocumentSearchApp();
});
