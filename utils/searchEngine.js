// utils/searchEngine.js
// 搜索引擎工具类

class SearchEngine {
  constructor() {
    this.engines = {
      baidu: {
        name: '百度',
        baseUrl: 'https://www.baidu.com/s',
        buildQuery: this.buildBaiduQuery.bind(this)
      },
      bing: {
        name: 'Bing',
        baseUrl: 'https://www.bing.com/search',
        buildQuery: this.buildBingQuery.bind(this)
      },
      google: {
        name: 'Google',
        baseUrl: 'https://www.google.com/search',
        buildQuery: this.buildGoogleQuery.bind(this)
      }
    };
  }

  // 构建百度搜索查询
  buildBaiduQuery(params) {
    let query = params.query;
    
    // 添加文件类型限制
    if (params.fileTypes && params.fileTypes.length > 0) {
      const fileTypeQuery = params.fileTypes.map(type => `filetype:${type}`).join(' OR ');
      query += ` (${fileTypeQuery})`;
    }
    
    // 添加站点限制
    if (params.site) {
      query += ` site:${params.site}`;
    }
    
    // 添加时间限制
    if (params.timeRange) {
      // 百度的时间限制参数
      const timeMap = {
        'day': '&gpc=stf%3D1%2Cstftype%3D1',
        'week': '&gpc=stf%3D7%2Cstftype%3D1',
        'month': '&gpc=stf%3D30%2Cstftype%3D1',
        'year': '&gpc=stf%3D365%2Cstftype%3D1'
      };
      // 这里需要在URL中添加时间参数
    }
    
    return {
      url: this.engines.baidu.baseUrl,
      params: {
        wd: query,
        rn: 50 // 每页结果数
      }
    };
  }

  // 构建Bing搜索查询
  buildBingQuery(params) {
    let query = params.query;
    
    // 添加文件类型限制
    if (params.fileTypes && params.fileTypes.length > 0) {
      const fileTypeQuery = params.fileTypes.map(type => `filetype:${type}`).join(' OR ');
      query += ` (${fileTypeQuery})`;
    }
    
    // 添加站点限制
    if (params.site) {
      query += ` site:${params.site}`;
    }
    
    return {
      url: this.engines.bing.baseUrl,
      params: {
        q: query,
        count: 50
      }
    };
  }

  // 构建Google搜索查询
  buildGoogleQuery(params) {
    let query = params.query;
    
    // 添加文件类型限制
    if (params.fileTypes && params.fileTypes.length > 0) {
      const fileTypeQuery = params.fileTypes.map(type => `filetype:${type}`).join(' OR ');
      query += ` (${fileTypeQuery})`;
    }
    
    // 添加站点限制
    if (params.site) {
      query += ` site:${params.site}`;
    }
    
    return {
      url: this.engines.google.baseUrl,
      params: {
        q: query,
        num: 50
      }
    };
  }

  // 获取搜索URL
  getSearchUrl(engine, params) {
    if (!this.engines[engine]) {
      throw new Error(`不支持的搜索引擎: ${engine}`);
    }
    
    const queryData = this.engines[engine].buildQuery(params);
    const url = new URL(queryData.url);
    
    Object.keys(queryData.params).forEach(key => {
      url.searchParams.append(key, queryData.params[key]);
    });
    
    return url.toString();
  }

  // 解析搜索结果（这里需要根据实际的API响应格式来实现）
  parseSearchResults(engine, htmlContent) {
    // 由于跨域限制，在小程序中直接解析HTML比较困难
    // 这里返回模拟数据，实际应用中需要后端API支持
    return this.generateMockResults();
  }

  // 生成模拟搜索结果
  generateMockResults() {
    const results = [];
    for (let i = 0; i < 10; i++) {
      results.push({
        id: `mock_${Date.now()}_${i}`,
        title: `搜索结果 ${i + 1} - 相关文档`,
        url: `https://example.com/document${i + 1}.pdf`,
        downloadUrl: `https://example.com/download/document${i + 1}.pdf`,
        description: '这是一个模拟的搜索结果描述，包含了相关的文档信息...',
        fileType: 'PDF',
        fileSize: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 9)}MB`,
        source: '示例网站',
        publishTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toLocaleDateString()
      });
    }
    return results;
  }

  // 验证搜索参数
  validateSearchParams(params) {
    if (!params.query || params.query.trim() === '') {
      throw new Error('搜索关键词不能为空');
    }
    
    if (!params.engine || !this.engines[params.engine]) {
      throw new Error('无效的搜索引擎');
    }
    
    return true;
  }

  // 获取支持的搜索引擎列表
  getSupportedEngines() {
    return Object.keys(this.engines).map(key => ({
      key,
      name: this.engines[key].name
    }));
  }

  // 获取支持的文件类型
  getSupportedFileTypes() {
    return [
      { type: 'pdf', label: 'PDF文档', icon: '📄' },
      { type: 'doc', label: 'Word文档', icon: '📝' },
      { type: 'docx', label: 'Word文档', icon: '📝' },
      { type: 'ppt', label: 'PowerPoint', icon: '📊' },
      { type: 'pptx', label: 'PowerPoint', icon: '📊' },
      { type: 'xls', label: 'Excel表格', icon: '📈' },
      { type: 'xlsx', label: 'Excel表格', icon: '📈' },
      { type: 'txt', label: '文本文件', icon: '📃' }
    ];
  }
}

// 导出单例
const searchEngine = new SearchEngine();
export default searchEngine;
