// utils/downloadManager.js
// 下载管理工具类

class DownloadManager {
  constructor() {
    this.downloadTasks = new Map();
    this.maxConcurrentDownloads = 3;
    this.currentDownloads = 0;
  }

  // 添加下载任务
  addDownload(file, options = {}) {
    const downloadId = `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const downloadTask = {
      id: downloadId,
      file,
      status: 'pending',
      progress: 0,
      startTime: null,
      endTime: null,
      error: null,
      options: {
        autoStart: true,
        retryCount: 3,
        timeout: 30000,
        ...options
      }
    };
    
    this.downloadTasks.set(downloadId, downloadTask);
    
    if (downloadTask.options.autoStart) {
      this.startDownload(downloadId);
    }
    
    return downloadId;
  }

  // 开始下载
  async startDownload(downloadId) {
    const task = this.downloadTasks.get(downloadId);
    if (!task) {
      throw new Error('下载任务不存在');
    }
    
    if (this.currentDownloads >= this.maxConcurrentDownloads) {
      task.status = 'queued';
      return;
    }
    
    task.status = 'downloading';
    task.startTime = Date.now();
    this.currentDownloads++;
    
    try {
      await this.performDownload(task);
      task.status = 'completed';
      task.endTime = Date.now();
    } catch (error) {
      task.status = 'failed';
      task.error = error.message;
      
      // 自动重试
      if (task.retryCount < task.options.retryCount) {
        task.retryCount = (task.retryCount || 0) + 1;
        setTimeout(() => {
          this.startDownload(downloadId);
        }, 2000 * task.retryCount);
      }
    } finally {
      this.currentDownloads--;
      this.processQueue();
    }
  }

  // 执行下载
  async performDownload(task) {
    return new Promise((resolve, reject) => {
      const downloadTask = wx.downloadFile({
        url: task.file.downloadUrl || task.file.url,
        timeout: task.options.timeout,
        success: (res) => {
          if (res.statusCode === 200) {
            // 保存文件
            wx.saveFile({
              tempFilePath: res.tempFilePath,
              success: (saveRes) => {
                task.localPath = saveRes.savedFilePath;
                task.progress = 100;
                resolve(saveRes);
              },
              fail: (err) => {
                reject(new Error('文件保存失败: ' + err.errMsg));
              }
            });
          } else {
            reject(new Error(`下载失败，状态码: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          reject(new Error('下载失败: ' + err.errMsg));
        }
      });

      // 监听下载进度
      downloadTask.onProgressUpdate((res) => {
        task.progress = res.progress;
        task.totalBytesWritten = res.totalBytesWritten;
        task.totalBytesExpectedToWrite = res.totalBytesExpectedToWrite;
        
        // 计算下载速度
        if (task.startTime) {
          const elapsed = (Date.now() - task.startTime) / 1000;
          const speed = res.totalBytesWritten / elapsed;
          task.speed = this.formatSpeed(speed);
        }
      });

      // 保存下载任务引用以便取消
      task.downloadTask = downloadTask;
    });
  }

  // 暂停下载
  pauseDownload(downloadId) {
    const task = this.downloadTasks.get(downloadId);
    if (task && task.downloadTask) {
      task.downloadTask.abort();
      task.status = 'paused';
      this.currentDownloads--;
      this.processQueue();
    }
  }

  // 继续下载
  resumeDownload(downloadId) {
    const task = this.downloadTasks.get(downloadId);
    if (task && task.status === 'paused') {
      this.startDownload(downloadId);
    }
  }

  // 取消下载
  cancelDownload(downloadId) {
    const task = this.downloadTasks.get(downloadId);
    if (task) {
      if (task.downloadTask) {
        task.downloadTask.abort();
        this.currentDownloads--;
      }
      task.status = 'cancelled';
      this.processQueue();
    }
  }

  // 删除下载任务
  removeDownload(downloadId) {
    const task = this.downloadTasks.get(downloadId);
    if (task) {
      if (task.downloadTask) {
        task.downloadTask.abort();
        this.currentDownloads--;
      }
      this.downloadTasks.delete(downloadId);
      this.processQueue();
    }
  }

  // 处理下载队列
  processQueue() {
    if (this.currentDownloads >= this.maxConcurrentDownloads) {
      return;
    }
    
    // 找到等待中的任务
    for (const [id, task] of this.downloadTasks) {
      if (task.status === 'queued' && this.currentDownloads < this.maxConcurrentDownloads) {
        this.startDownload(id);
        break;
      }
    }
  }

  // 获取下载任务信息
  getDownloadInfo(downloadId) {
    return this.downloadTasks.get(downloadId);
  }

  // 获取所有下载任务
  getAllDownloads() {
    return Array.from(this.downloadTasks.values());
  }

  // 获取下载统计
  getDownloadStats() {
    const tasks = this.getAllDownloads();
    return {
      total: tasks.length,
      pending: tasks.filter(t => t.status === 'pending').length,
      queued: tasks.filter(t => t.status === 'queued').length,
      downloading: tasks.filter(t => t.status === 'downloading').length,
      paused: tasks.filter(t => t.status === 'paused').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      failed: tasks.filter(t => t.status === 'failed').length,
      cancelled: tasks.filter(t => t.status === 'cancelled').length
    };
  }

  // 清除已完成的下载
  clearCompleted() {
    for (const [id, task] of this.downloadTasks) {
      if (task.status === 'completed') {
        this.downloadTasks.delete(id);
      }
    }
  }

  // 清除失败的下载
  clearFailed() {
    for (const [id, task] of this.downloadTasks) {
      if (task.status === 'failed') {
        this.downloadTasks.delete(id);
      }
    }
  }

  // 清除所有下载
  clearAll() {
    // 取消所有进行中的下载
    for (const [id, task] of this.downloadTasks) {
      if (task.downloadTask) {
        task.downloadTask.abort();
      }
    }
    this.downloadTasks.clear();
    this.currentDownloads = 0;
  }

  // 格式化下载速度
  formatSpeed(bytesPerSecond) {
    if (bytesPerSecond < 1024) {
      return `${bytesPerSecond.toFixed(0)} B/s`;
    } else if (bytesPerSecond < 1024 * 1024) {
      return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
    } else {
      return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
    }
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else if (bytes < 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    } else {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
  }

  // 设置最大并发下载数
  setMaxConcurrentDownloads(max) {
    this.maxConcurrentDownloads = Math.max(1, Math.min(10, max));
  }
}

// 导出单例
const downloadManager = new DownloadManager();
export default downloadManager;
