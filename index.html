<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档搜索下载工具</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📄</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 头部导航 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">📄</span>
                    文档搜索工具
                </h1>
                <nav class="nav-tabs">
                    <button class="nav-tab active" data-tab="search">
                        <span class="tab-icon">🔍</span>
                        搜索
                    </button>
                    <button class="nav-tab" data-tab="downloads">
                        <span class="tab-icon">📥</span>
                        下载
                        <span class="download-badge" id="downloadBadge">0</span>
                    </button>
                </nav>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="app-main">
            <!-- 搜索页面 -->
            <section class="tab-content active" id="search-tab">
                <div class="container">
                    <!-- 搜索卡片 -->
                    <div class="card search-card">
                        <div class="card-header">
                            <h2>文档搜索</h2>
                        </div>
                        <div class="card-body">
                            <!-- 搜索框 -->
                            <div class="form-group">
                                <label for="searchInput">搜索关键词</label>
                                <div class="search-input-group">
                                    <input 
                                        type="text" 
                                        id="searchInput" 
                                        class="form-control" 
                                        placeholder="请输入搜索关键词"
                                        autocomplete="off"
                                    >
                                    <button class="search-btn" id="searchBtn">
                                        <span class="btn-icon">🔍</span>
                                        搜索
                                    </button>
                                </div>
                            </div>

                            <!-- 文件类型选择 -->
                            <div class="form-group">
                                <label>文件类型</label>
                                <div class="filetype-selector">
                                    <button class="tag-btn active" data-type="pdf">PDF</button>
                                    <button class="tag-btn active" data-type="doc">Word</button>
                                    <button class="tag-btn" data-type="ppt">PPT</button>
                                    <button class="tag-btn" data-type="xls">Excel</button>
                                    <button class="tag-btn" data-type="txt">TXT</button>
                                </div>
                            </div>

                            <!-- 站点选择 -->
                            <div class="form-group">
                                <label>搜索范围</label>
                                <div class="site-selector">
                                    <button class="tag-btn active" data-site="">全网搜索</button>
                                    <button class="tag-btn" data-site="weixin.qq.com">微信公众号</button>
                                    <button class="tag-btn" data-site="zsxq.com">知识星球</button>
                                    <button class="tag-btn" data-site="jianshu.com">简书</button>
                                    <button class="tag-btn" data-site="zhihu.com">知乎</button>
                                    <button class="tag-btn" data-site="csdn.net">CSDN</button>
                                </div>
                            </div>

                            <!-- 高级选项 -->
                            <div class="advanced-options" id="advancedOptions">
                                <!-- 搜索引擎选择 -->
                                <div class="form-group">
                                    <label>搜索引擎</label>
                                    <div class="engine-selector">
                                        <button class="tag-btn active" data-engine="baidu">百度</button>
                                        <button class="tag-btn" data-engine="bing">Bing</button>
                                        <button class="tag-btn" data-engine="google">Google</button>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="customSite">自定义站点 (可选)</label>
                                    <input
                                        type="text"
                                        id="customSite"
                                        class="form-control"
                                        placeholder="如: example.com"
                                    >
                                    <small class="form-hint">输入自定义站点会覆盖上面的站点选择</small>
                                </div>

                                <div class="form-group">
                                    <label for="timeRange">时间范围</label>
                                    <select id="timeRange" class="form-control">
                                        <option value="">不限时间</option>
                                        <option value="day">最近一天</option>
                                        <option value="week">最近一周</option>
                                        <option value="month">最近一月</option>
                                        <option value="year">最近一年</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 高级选项切换 -->
                            <div class="advanced-toggle" id="advancedToggle">
                                <span>展开高级选项</span>
                                <span class="toggle-arrow">▼</span>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷搜索 -->
                    <div class="card">
                        <div class="card-header">
                            <h3>快捷搜索</h3>
                        </div>
                        <div class="card-body">
                            <div class="quick-search-grid">
                                <button class="quick-search-item" data-preset="academic">
                                    <span class="quick-icon">📄</span>
                                    <div class="quick-content">
                                        <div class="quick-title">学术论文</div>
                                        <div class="quick-desc">搜索PDF格式的学术论文</div>
                                    </div>
                                </button>
                                <button class="quick-search-item" data-preset="report">
                                    <span class="quick-icon">📊</span>
                                    <div class="quick-content">
                                        <div class="quick-title">行业报告</div>
                                        <div class="quick-desc">搜索行业分析报告</div>
                                    </div>
                                </button>
                                <button class="quick-search-item" data-preset="wechat">
                                    <span class="quick-icon">📱</span>
                                    <div class="quick-content">
                                        <div class="quick-title">微信公众号</div>
                                        <div class="quick-desc">搜索公众号文章</div>
                                    </div>
                                </button>
                                <button class="quick-search-item" data-preset="tech">
                                    <span class="quick-icon">📚</span>
                                    <div class="quick-content">
                                        <div class="quick-title">技术文档</div>
                                        <div class="quick-desc">搜索技术文档和API</div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索历史 -->
                    <div class="card" id="historyCard" style="display: none;">
                        <div class="card-header">
                            <h3>搜索历史</h3>
                            <button class="clear-btn" id="clearHistory">清空</button>
                        </div>
                        <div class="card-body">
                            <div class="history-list" id="historyList"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 下载页面 -->
            <section class="tab-content" id="downloads-tab">
                <div class="container">
                    <!-- 下载统计 -->
                    <div class="card stats-card">
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number" id="totalDownloads">0</div>
                                    <div class="stat-label">总下载</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="activeDownloads">0</div>
                                    <div class="stat-label">下载中</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="completedDownloads">0</div>
                                    <div class="stat-label">已完成</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="failedDownloads">0</div>
                                    <div class="stat-label">失败</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 下载列表 -->
                    <div class="download-list" id="downloadList">
                        <!-- 下载项目将通过JavaScript动态添加 -->
                    </div>

                    <!-- 空状态 -->
                    <div class="empty-state" id="emptyDownloads">
                        <div class="empty-icon">📥</div>
                        <div class="empty-text">
                            暂无下载任务<br>
                            去搜索页面添加下载吧
                        </div>
                        <button class="btn btn-primary" onclick="switchTab('search')">开始搜索</button>
                    </div>
                </div>
            </section>
        </main>

        <!-- 搜索结果模态框 -->
        <div class="modal" id="resultsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>搜索结果</h3>
                    <button class="modal-close" id="closeResults">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="results-info">
                        <span id="resultsCount">找到 0 个结果</span>
                        <div class="results-actions">
                            <button class="btn btn-sm" id="selectAllResults">全选</button>
                            <button class="btn btn-sm btn-primary" id="batchDownload" disabled>批量下载</button>
                        </div>
                    </div>
                    <div class="results-list" id="resultsList">
                        <!-- 搜索结果将通过JavaScript动态添加 -->
                    </div>
                    <div class="loading" id="searchLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        <span>搜索中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
